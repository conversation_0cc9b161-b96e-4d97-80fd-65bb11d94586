import requests

# === Configuration ===
FB_PAGE_ID = "your_facebook_page_id"
IG_USER_ID = "your_instagram_business_account_id"
LINKEDIN_ORG_ID = "your_linkedin_organization_id"
IMAGE_URL = "logo.jpg"
MESSAGE = "🚀 New Post: Check out our latest update!"
ACCESS_TOKEN_FB = "your_long_lived_facebook_token"
ACCESS_TOKEN_LINKEDIN = "your_linkedin_access_token"

# === Facebook Post ===
def post_to_facebook():
    url = f"https://graph.facebook.com/{FB_PAGE_ID}/photos"
    data = {
        'url': IMAGE_URL,
        'caption': MESSAGE,
        'access_token': ACCESS_TOKEN_FB
    }
    res = requests.post(url, data=data)
    print("Facebook:", res.json())

# === Instagram Post ===
def post_to_instagram():
    # Step 1: Create Media Object
    create_url = f"https://graph.facebook.com/v19.0/{IG_USER_ID}/media"
    media_payload = {
        'image_url': IMAGE_URL,
        'caption': MESSAGE,
        'access_token': ACCESS_TOKEN_FB
    }
    create_res = requests.post(create_url, data=media_payload)
    creation_id = create_res.json().get("id")
    
    if not creation_id:
        print("Instagram Media Creation Failed:", create_res.json())
        return
    
    # Step 2: Publish Media
    publish_url = f"https://graph.facebook.com/v19.0/{IG_USER_ID}/media_publish"
    publish_payload = {
        'creation_id': creation_id,
        'access_token': ACCESS_TOKEN_FB
    }
    publish_res = requests.post(publish_url, data=publish_payload)
    print("Instagram:", publish_res.json())

# === LinkedIn Post ===
def post_to_linkedin():
    post_url = "https://api.linkedin.com/v2/ugcPosts"
    headers = {
        "Authorization": f"Bearer {ACCESS_TOKEN_LINKEDIN}",
        "Content-Type": "application/json",
        "X-Restli-Protocol-Version": "2.0.0"
    }
    payload = {
        "author": f"urn:li:organization:{LINKEDIN_ORG_ID}",
        "lifecycleState": "PUBLISHED",
        "specificContent": {
            "com.linkedin.ugc.ShareContent": {
                "shareCommentary": {
                    "text": MESSAGE
                },
                "shareMediaCategory": "IMAGE",
                "media": [{
                    "status": "READY",
                    "originalUrl": IMAGE_URL
                }]
            }
        },
        "visibility": {
            "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC"
        }
    }
    res = requests.post(post_url, headers=headers, json=payload)
    print("LinkedIn:", res.json())

# === Execute All ===
if __name__ == "__main__":
    post_to_facebook()
    post_to_instagram()
    post_to_linkedin()
