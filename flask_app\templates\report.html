<!-- templates/report.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Snapshot: Monthly Review</title>
    <!-- Add in head -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a3a6c, #2c5282);
            color: #333;
            padding: 20px;
            min-height: 100vh;
        }
        
        .report-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1a3a6c, #2c5282);
            color: white;
            padding: 10px 15px;
            position: relative;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .header h1 {
            font-size: 1.2rem;
            margin-bottom: 5px;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 0.85rem;
            margin-bottom: 2px;
            opacity: 0.9;
            max-width: 600px;
        }
        
        .date-controls {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
        
        .date-form {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .date-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }
        
        .date-label {
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .date-input {
            padding: 10px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
            font-size: 1rem;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 15px;
            justify-content: flex-end;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #4caf50;
            color: white;
        }
        
        .btn-secondary {
            background: #2196f3;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }
        
        .report-info {
            background-color: #e3f2fd;
            padding: 8px 15px;
            font-size: 0.75rem;
            border-bottom: 1px solid #bbdefb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .report-period {
            font-weight: bold;
            color: #1a3a6c;
            font-size: 1.1rem;
        }
        
        .contact-link {
            color: #1a3a6c;
            text-decoration: none;
            font-weight: 500;
        }
        
        .contact-link i {
            margin-right: 5px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 6px;
            padding: 10px 15px;
            background-color: white;
        }
        
        .metric-card {
            background: linear-gradient(to bottom, #f5f7fa, #e3e7f0);
            border-radius: 10px;
            padding: 8px 6px;
            font-size: 0.75rem;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.1);
            background: linear-gradient(to bottom, #e3e7f0, #d5dae8);
        }
        
        .metric-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #1a3a6c;
            margin: 6px 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .metric-label {
            font-size: 0.75rem;
            color: #546e7a;
            font-weight: 500;
        }
        
        .chart-section {
            background-color: #f8f9fa;
            padding: 10px 15px;
        }
        
        .chart-row {
            display: grid;
            gap: 20px;
            grid-template-columns: repeat(3, 1fr);
            margin-bottom: 20px;
        }
        
        .chart-container {
            background-color: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            flex: 1;
        }
        .chart-row.custom-chart-layout {
        display: grid;
        grid-template-columns: 30% 40% 30%;
        gap: 10px;
        margin-bottom: 20px;
        }

        .chart-row.custom-chart-layout .chart-container {
        padding: 10px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0,0,0,0.05);
        }

        .chart-row.custom-chart-layout canvas {
        width: 100% !important;
        height: 180px !important;
        }

        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .chart-title {
            font-size: 1.4rem;
            color: #1a3a6c;
            font-weight: 600;
        }
        
        .chart-main {
            height: 240px;
            margin-bottom: 10px;
        }
        
        .chart-data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: white;
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .chart-data-table th {
            background-color: #1a3a6c;
            color: white;
            text-align: left;
            padding: 12px 15px;
            font-weight: 500;
        }
        
        .chart-data-table tr:nth-child(even) {
            background-color: #f5f7fa;
        }
        
        .chart-data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .data-section {
            padding: 20px;
            background-color: white;
        }
        
        .section-title {
            font-size: 1rem;
            color: #1a3a6c;
            margin-bottom: 6px;
            padding-bottom: 4px;
            border-bottom: 3px solid #4caf50;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            font-size: 1.2rem;
            margin-bottom: 10px;
            padding-bottom: 5px;
            margin-right: 10px;
            color: #4caf50;
        }
        
        .data-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .data-table th {
            background-color: #1a3a6c;
            color: white;
            text-align: left;
            font-weight: 500;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .data-table th, .data-table td {
            padding: 5px 8px;
            font-size: 0.75rem;
        }
        .data-table td {
            border-bottom: 1px solid #e0e0e0;
        }
        
        .footer {
            background: linear-gradient(135deg, #1a3a6c, #2c5282);
            color: white;
            text-align: center;
            padding: 25px;
            font-size: 1rem;
        }
        
        .info-box {
            background-color: #e3f2fd;
            border-left: 5px solid #1a3a6c;
            padding: 12px 15px;
            margin: 10px 15px;
            font-size: 0.8rem;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        
        .info-box p {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .info-box p:last-child {
            margin-bottom: 0;
        }
        
        .page-title {
            text-align: center;
            margin: 20px 0 30px;
            color: white;
            font-size: 2.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        @media (max-width: 992px) {
            .metrics-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .chart-row {
                flex-direction: column;
            }
            
            .data-grid {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                align-items: flex-start;
            }
        }
        
        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header h1 {
                font-size: 1.8rem;
            }
            
            .date-form {
                flex-direction: column;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
        @media print {
        .screen-only { display: none !important; }
        }

    </style>
</head>
<body>
    
    <div class="report-container" id="report-content">
        
        <div class="header">    
            <div class="header-content">
                <div>
                    <h1>SEO Performance Dashboard</h1>
                    <p>How is my campaign performing?</p>
                </div>
                
                <div class="date-controls">
                    <form id="dateForm" class="date-form">
                        <div class="date-group">
                            <label class="date-label">Start Date:</label>
                            <input type="date" id="startDate" name="start_date" class="date-input" value="{{ start_date }}">
                        </div>
                        
                        <div class="date-group">
                            <label class="date-label">End Date:</label>
                            <input type="date" id="endDate" name="end_date" class="date-input" value="{{ end_date }}">
                        </div>
                        
                        <div class="action-buttons">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sync-alt"></i> Update Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="report-info">
            <div class="report-period">
                <i class="fas fa-calendar-alt"></i> Report Period: {{ period }}
            </div>
            <a href="#" class="contact-link">
                <i class="fas fa-headset"></i> Contact Your Digital Presence Manager
            </a>
        </div>
        
        <div class="info-box">
            <p>The metrics in this report are derived from Google Analytics and Google Search Console. Filters may be applied to clean up traffic and avoid over-reporting on organic traffic.</p>
            <p>Your Digital Presence Manager may contact you about upcoming work requiring feedback on website content & technical optimizations. Timely responses are critical for SEO campaign performance.</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-label">Impressions</div>
                <div class="metric-value">{{ metrics.impressions }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Clicks</div>
                <div class="metric-value">{{ metrics.clicks }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Total Users</div>
                <div class="metric-value">{{ metrics.total_users }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">New Users</div>
                <div class="metric-value">{{ metrics.new_users }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Sessions</div>
                <div class="metric-value">{{ metrics.sessions }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Engaged Sessions</div>
                <div class="metric-value">{{ metrics.engaged_sessions }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Engagement Rate</div>
                <div class="metric-value">{{ metrics.engagement_rate }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Avg. Engagement</div>
                <div class="metric-value">{{ metrics.avg_engagement }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Event Count</div>
                <div class="metric-value">{{ metrics.event_count }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Events/Session</div>
                <div class="metric-value">{{ metrics.events_per_session }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Pageviews</div>
                <div class="metric-value">{{ metrics.pageviews }}</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Views/Session</div>
                <div class="metric-value">{{ metrics.views_per_session }}</div>
            </div>
        </div>
        
        <div class="chart-section">
            
            <div class="chart-row custom-chart-layout">
                <div class="chart-container">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-chart-pie"></i> Sessions by Channel
                        </div>
                    </div>
                    <div class="chart-main">
                        <canvas id="channelChart"></canvas>
                    </div>
                    <table class="chart-data-table">
                        <thead>
                            <tr>
                                <th>Channel</th>
                                <th>Sessions</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for i in range(channel_data.labels|length) %}
                            <tr>
                                <td>{{ channel_data.labels[i] }}</td>
                                <td>{{ channel_data.sessions[i] }}</td>
                                <td>{{ channel_data.percentages[i] }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="chart-container">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-chart-line"></i> Sessions vs Engaged Sessions
                        </div>
                    </div>
                    <div class="chart-main">
                        <canvas id="sessionsChart"></canvas>
                    </div>
                    <table class="chart-data-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Sessions</th>
                                <th>Engaged Sessions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for i in range(time_series.dates|length) %}
                            <tr>
                                <td>{{ time_series.dates[i] }}</td>
                                <td>{{ time_series.sessions[i] }}</td>
                                <td>{{ time_series.engaged_sessions[i] }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="chart-container">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-mobile-alt"></i> Sessions by Device
                        </div>
                    </div>
                    <div class="chart-main">
                        <canvas id="deviceChart"></canvas>
                    </div>
                    <table class="chart-data-table">
                        <thead>
                            <tr>
                                <th>Device</th>
                                <th>Sessions</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for i in range(device_data.labels|length) %}
                            <tr>
                                <td>{{ device_data.labels[i] }}</td>
                                <td>{{ device_data.sessions[i] }}</td>
                                <td>{{ device_data.percentages[i] }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="data-section">
            <div class="data-grid" style="grid-template-columns: repeat(3, 1fr);">
                <div>
                    <h2 class="section-title">
                <i class="fas fa-search"></i> Top Search Queries
                    </h2>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Query</th>
                                <th>Impressions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for query in search_queries %}
                            <tr>
                                <td>{{ query.query }}</td>
                                <td>{{ query.impressions }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div>
                    <h2 class="section-title" style="margin-top: 0;">
                        <i class="fas fa-city"></i> Top Cities by Sessions
                    </h2>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>City</th>
                                <th>Sessions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for city in top_cities %}
                            <tr>
                                <td>{{ city.city }}</td>
                                <td>{{ city.sessions }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="data-section">
                    <h2 class="section-title">
                        <i class="fas fa-file-alt"></i> Top Landing Pages
                    </h2>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Page</th>
                                <th>Views</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for page in landing_pages %}
                            <tr>
                                <td>{{ page.page }}</td>
                                <td>{{ page.views }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        </div>
        <div class="screen-only"> 
        <div class="footer">
            <p>For questions regarding this data or your SEO program in general, please contact your Digital Presence Manager (DPM)</p>
            <p>Generated on: {{ report_date }}</p>
        </div>
        </div>
    </div>
    
    <script>
        // Line Chart: Sessions vs Engaged Sessions
        const sessionsCtx = document.getElementById('sessionsChart').getContext('2d');
        const sessionsChart = new Chart(sessionsCtx, {
            type: 'line',
            data: {
                labels: {{ time_series.dates | tojson }},
                datasets: [
                    {
                        label: 'Sessions',
                        data: {{ time_series.sessions | tojson }},
                        borderColor: '#1f77b4',
                        backgroundColor: 'rgba(31, 119, 180, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        pointBackgroundColor: '#1f77b4',
                        pointRadius: 5
                    },
                    {
                        label: 'Engaged Sessions',
                        data: {{ time_series.engaged_sessions | tojson }},
                        borderColor: '#ff7f0e',
                        backgroundColor: 'rgba(255, 127, 14, 0.1)',
                        borderWidth: 3,
                        tension: 0.3,
                        fill: true,
                        pointBackgroundColor: '#ff7f0e',
                        pointRadius: 5
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            font: {
                                size: 12
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 12
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            font: {
                                size: 13
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleFont: {
                            size: 14
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 10
                    }
                }
            }
        });

        // Pie Chart: Sessions by Channel
        const channelCtx = document.getElementById('channelChart').getContext('2d');
        const channelChart = new Chart(channelCtx, {
            type: 'doughnut',
            data: {
                labels: {{ channel_data.labels | tojson }},
                datasets: [{
                    data: {{ channel_data.percentages | tojson }},
                    backgroundColor: [
                        '#1f77b4',
                        '#ff7f0e',
                        '#2ca02c',
                        '#d62728',
                        '#9467bd'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 13
                            },
                            boxWidth: 15,
                            padding: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed}%`;
                            }
                        }
                    }
                },
                cutout: '50%'
            }
        });

        // Pie Chart: Sessions by Device
        const deviceCtx = document.getElementById('deviceChart').getContext('2d');
        const deviceChart = new Chart(deviceCtx, {
            type: 'doughnut',
            data: {
                labels: {{ device_data.labels | tojson }},
                datasets: [{
                    data: {{ device_data.percentages | tojson }},
                    backgroundColor: [
                        '#1f77b4',
                        '#ff7f0e',
                        '#2ca02c'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 13
                            },
                            boxWidth: 15,
                            padding: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed}%`;
                            }
                        }
                    }
                },
                cutout: '50%'
            }
        });

        // Date form submission
        document.getElementById('dateForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (!startDate || !endDate) {
                alert('Please select both start and end dates');
                return;
            }
            
            if (new Date(startDate) > new Date(endDate)) {
                alert('Start date cannot be after end date');
                return;
            }
            
            window.location.href = `/?start_date=${startDate}&end_date=${endDate}`;
        });
        
        // Set date limits
        window.addEventListener('load', function() {
            const today = new Date().toISOString().split('T')[0];
            const oneYearAgo = new Date();
            oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
            const oneYearAgoStr = oneYearAgo.toISOString().split('T')[0];
            
            document.getElementById('startDate').setAttribute('max', today);
            document.getElementById('endDate').setAttribute('max', today);
            document.getElementById('startDate').setAttribute('min', oneYearAgoStr);
            document.getElementById('endDate').setAttribute('min', oneYearAgoStr);
        });
    </script>
</body>
</html>