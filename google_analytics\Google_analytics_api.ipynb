{"cells": [{"cell_type": "markdown", "id": "4c1f26cc", "metadata": {}, "source": ["# Google analytics data api\n", "\n", "### sample data retriving"]}, {"cell_type": "code", "execution_count": 1, "id": "d3cd7538", "metadata": {}, "outputs": [], "source": ["# Initial setup\n", "\n", "from google.analytics.data_v1beta import BetaAnalyticsDataClient\n", "from google.analytics.data_v1beta.types import (\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    Dimension,\n", "    <PERSON><PERSON>,\n", "    RunReportRequest\n", ")\n", "from google.oauth2 import service_account\n", "\n", "# === CONFIGURATION ===\n", "KEY_FILE_PATH = \"fabled-tesla-453318-h3-d12274a3efcf.json\"  # 👈 Replace with your JSON key path\n", "PROPERTY_ID = \"*********\"                      # 👈 Replace with your GA4 property ID\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "f216af17", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Channel Group        | Sessions | Users\n", "----------------------------------------\n", "Direct               | 2        | 2\n"]}], "source": ["# === AUTHENTICATE ===\n", "credentials = service_account.Credentials.from_service_account_file(KEY_FILE_PATH)\n", "client = BetaAnalyticsDataClient(credentials=credentials)\n", "\n", "# === DEFINE REQUEST ===\n", "request = RunReportRequest(\n", "    property=f\"properties/{PROPERTY_ID}\",\n", "    dimensions=[Dimension(name=\"sessionDefaultChannelGroup\")],\n", "    metrics=[\n", "        <PERSON><PERSON>(name=\"sessions\"),\n", "        Metric(name=\"activeUsers\")  # <-- use this instead of 'users'\n", "    ],\n", "    date_ranges=[DateRange(start_date=\"2025-06-01\", end_date=\"2025-06-10\")],\n", ")\n", "\n", "# === GET REPORT ===\n", "response = client.run_report(request)\n", "\n", "# === DISPLAY RESULTS ===\n", "print(\"Channel Group        | Sessions | Users\")\n", "print(\"----------------------------------------\")\n", "for row in response.rows:\n", "    channel = row.dimension_values[0].value\n", "    if channel in [\"Organic Search\", \"Referral\", \"Direct\"]:\n", "        sessions = row.metric_values[0].value\n", "        users = row.metric_values[1].value\n", "        print(f\"{channel:<20} | {sessions:<8} | {users}\")\n"]}, {"cell_type": "code", "execution_count": 3, "id": "21483b85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Google Analytics Report (by Channel Group):\n", "       Channel  Sessions  Active Users  New Users\n", "   Paid Search      4890          4449       4448\n", " Cross-network       621           573        570\n", "        Direct        98            91         87\n", "    <PERSON>id <PERSON>        34            30         29\n", "Organic Search         4             4          4\n", "    Unassigned         4             4          0\n", " Organic Video         2             2          2\n", "      Referral         1             1          1\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# To retrive all data from the dashboard\n", "\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from google.analytics.data_v1beta import BetaAnalyticsDataClient\n", "from google.analytics.data_v1beta.types import DateRange, Dimension, Metric, RunReportRequest\n", "from google.oauth2 import service_account\n", "\n", "# ==== CONFIGURATION ====\n", "KEY_FILE_PATH = \"fabled-tesla-453318-h3-d12274a3efcf.json\"  # 👈 Replace with your JSON key path\n", "PROPERTY_ID = \"*********\"     \n", "START_DATE = \"2024-03-01\"\n", "END_DATE = \"2024-03-31\"\n", "\n", "# ==== AUTHENTICATION ====\n", "credentials = service_account.Credentials.from_service_account_file(KEY_FILE_PATH)\n", "client = BetaAnalyticsDataClient(credentials=credentials)\n", "\n", "# ==== API REQUEST ====\n", "request = RunReportRequest(\n", "    property=f\"properties/{PROPERTY_ID}\",\n", "    dimensions=[Dimension(name=\"sessionDefaultChannelGroup\")],\n", "    metrics=[\n", "        <PERSON><PERSON>(name=\"sessions\"),\n", "        Met<PERSON>(name=\"activeUsers\"),\n", "        Metric(name=\"newUsers\"),\n", "    ],\n", "    date_ranges=[DateRange(start_date=START_DATE, end_date=END_DATE)],\n", ")\n", "\n", "response = client.run_report(request)\n", "\n", "# ==== PARSE RESPONSE ====\n", "data = []\n", "for row in response.rows:\n", "    channel = row.dimension_values[0].value\n", "    sessions = int(row.metric_values[0].value)\n", "    active_users = int(row.metric_values[1].value)\n", "    new_users = int(row.metric_values[2].value)\n", "    data.append({\n", "        \"Channel\": channel,\n", "        \"Sessions\": sessions,\n", "        \"Active Users\": active_users,\n", "        \"New Users\": new_users\n", "    })\n", "\n", "df = pd.DataFrame(data)\n", "\n", "# ==== DISPLAY TABLE ====\n", "print(\"\\n📊 Google Analytics Report (by Channel Group):\")\n", "print(df.sort_values(\"Sessions\", ascending=False).to_string(index=False))\n", "\n", "# ==== PLOT GRAPH ====\n", "plt.figure(figsize=(10, 6))\n", "plt.bar(df[\"Channel\"], df[\"Sessions\"], color=\"skyblue\", label=\"Sessions\")\n", "plt.plot(df[\"Channel\"], df[\"Active Users\"], marker='o', color=\"green\", label=\"Active Users\")\n", "plt.plot(df[\"Channel\"], df[\"New Users\"], marker='x', color=\"orange\", label=\"New Users\")\n", "\n", "plt.title(f\"GA4 Traffic Report ({START_DATE} to {END_DATE})\")\n", "plt.xlabel(\"Channel Group\")\n", "plt.ylabel(\"Count\")\n", "plt.legend()\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 13, "id": "5b5a905d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Authentication successful!\n"]}], "source": ["# STEP 1: Setup and Authentication\n", "\n", "from google.oauth2 import service_account\n", "from google.analytics.data_v1beta import BetaAnalyticsDataClient\n", "from google.analytics.data_v1beta.types import DateRange, Metric, Dimension, RunReportRequest\n", "from googleapiclient.discovery import build\n", "import pandas as pd\n", "\n", "# Path to your service account key file (update this!)\n", "KEY_FILE_PATH = \"fabled-tesla-453318-h3-d12274a3efcf.json\"  # 👈 Replace with your JSON key path\n", "GA4_PROPERTY_ID = \"*********\"  \n", "\n", "# Search Console verified site URL (must start with 'https://')\n", "SITE_URL = 'https://partners.ycuuk.com/'  # Replace with your actual verified site\n", "\n", "# Authenticate with service account\n", "credentials = service_account.Credentials.from_service_account_file(\n", "    KEY_FILE_PATH,\n", "    scopes=[\"https://www.googleapis.com/auth/analytics.readonly\",\n", "            \"https://www.googleapis.com/auth/webmasters.readonly\"]\n", ")\n", "\n", "# Google Analytics Data API Client\n", "ga_client = BetaAnalyticsDataClient(credentials=credentials)\n", "\n", "# Google Search Console API Client\n", "search_console = build('searchconsole', 'v1', credentials=credentials)\n", "\n", "print(\"✅ Authentication successful!\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "be1e0c92", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'GA4_PROPERTY_ID' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 11\u001b[39m\n\u001b[32m      7\u001b[39m start_date = end_date - timedelta(days=\u001b[32m30\u001b[39m)\n\u001b[32m      9\u001b[39m \u001b[38;5;66;03m# Updated metrics list (correct + supported ones)\u001b[39;00m\n\u001b[32m     10\u001b[39m request = RunReportRequest(\n\u001b[32m---> \u001b[39m\u001b[32m11\u001b[39m     \u001b[38;5;28mproperty\u001b[39m=\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mproperties/\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[43mGA4_PROPERTY_ID\u001b[49m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m,\n\u001b[32m     12\u001b[39m     dimensions=[],\n\u001b[32m     13\u001b[39m     metrics=[\n\u001b[32m     14\u001b[39m         Metric(name=\u001b[33m\"\u001b[39m\u001b[33mtotalUsers\u001b[39m\u001b[33m\"\u001b[39m),\n\u001b[32m     15\u001b[39m         Metric(name=\u001b[33m\"\u001b[39m\u001b[33mnewUsers\u001b[39m\u001b[33m\"\u001b[39m),\n\u001b[32m     16\u001b[39m         Metric(name=\u001b[33m\"\u001b[39m\u001b[33msessions\u001b[39m\u001b[33m\"\u001b[39m),\n\u001b[32m     17\u001b[39m         Metric(name=\u001b[33m\"\u001b[39m\u001b[33mengagedSessions\u001b[39m\u001b[33m\"\u001b[39m),\n\u001b[32m     18\u001b[39m         Metric(name=\u001b[33m\"\u001b[39m\u001b[33mengagementRate\u001b[39m\u001b[33m\"\u001b[39m),\n\u001b[32m     19\u001b[39m         Metric(name=\u001b[33m\"\u001b[39m\u001b[33maverageSessionDuration\u001b[39m\u001b[33m\"\u001b[39m),  \u001b[38;5;66;03m# ✅ Correct replacement\u001b[39;00m\n\u001b[32m     20\u001b[39m         Metric(name=\u001b[33m\"\u001b[39m\u001b[33meventCount\u001b[39m\u001b[33m\"\u001b[39m),\n\u001b[32m     21\u001b[39m         Metric(name=\u001b[33m\"\u001b[39m\u001b[33mscreenPageViews\u001b[39m\u001b[33m\"\u001b[39m),\n\u001b[32m     22\u001b[39m     ],\n\u001b[32m     23\u001b[39m     date_ranges=[DateRange(start_date=\u001b[38;5;28mstr\u001b[39m(start_date), end_date=\u001b[38;5;28mstr\u001b[39m(end_date))]\n\u001b[32m     24\u001b[39m )\n\u001b[32m     26\u001b[39m \u001b[38;5;66;03m# Run the report\u001b[39;00m\n\u001b[32m     27\u001b[39m response = ga_client.run_report(request)\n", "\u001b[31mNameError\u001b[39m: name 'GA4_PROPERTY_ID' is not defined"]}], "source": ["# STEP 2A (FIXED): Fetch Summary Metrics from GA4 (Last 30 Days)\n", "\n", "from datetime import datetime, timedelta\n", "\n", "# Define the date range\n", "end_date = datetime.today().date()\n", "start_date = end_date - <PERSON><PERSON><PERSON>(days=30)\n", "\n", "# Updated metrics list (correct + supported ones)\n", "request = RunReportRequest(\n", "    property=f\"properties/{GA4_PROPERTY_ID}\",\n", "    dimensions=[],\n", "    metrics=[\n", "        Met<PERSON>(name=\"totalUsers\"),\n", "        Metric(name=\"newUsers\"),\n", "        <PERSON><PERSON>(name=\"sessions\"),\n", "        <PERSON><PERSON>(name=\"engagedSessions\"),\n", "        <PERSON><PERSON>(name=\"engagementRate\"),\n", "        Metric(name=\"averageSessionDuration\"),  # ✅ Correct replacement\n", "        <PERSON><PERSON>(name=\"eventCount\"),\n", "        Metric(name=\"screenPageViews\"),\n", "    ],\n", "    date_ranges=[DateRange(start_date=str(start_date), end_date=str(end_date))]\n", ")\n", "\n", "# Run the report\n", "response = ga_client.run_report(request)\n", "\n", "# Parse response into readable dictionary\n", "summary_data = {}\n", "for metric_header, row in zip(response.metric_headers, response.rows[0].metric_values):\n", "    summary_data[metric_header.name] = float(row.value)\n", "\n", "# Convert to DataFrame\n", "summary_df = pd.DataFrame([summary_data])\n", "summary_df\n"]}, {"cell_type": "code", "execution_count": 15, "id": "8496d0f6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>sessions</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-05-15</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-05-16</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-06-03</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-06-07</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-06-11</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date  sessions\n", "1  2025-05-15         1\n", "2  2025-05-16         1\n", "3  2025-06-03         1\n", "0  2025-06-07         2\n", "4  2025-06-11         1"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# STEP 2B: Fetch Daily Sessions for the Last 30 Days (for Graph)\n", "\n", "# Create report request with dimensions by date\n", "request = RunReportRequest(\n", "    property=f\"properties/{GA4_PROPERTY_ID}\",\n", "    dimensions=[Dimension(name=\"date\")],\n", "    metrics=[Metric(name=\"sessions\")],\n", "    date_ranges=[DateRange(start_date=str(start_date), end_date=str(end_date))]\n", ")\n", "\n", "# Run the report\n", "response = ga_client.run_report(request)\n", "\n", "# Parse response\n", "daily_data = []\n", "for row in response.rows:\n", "    daily_data.append({\n", "        \"date\": datetime.strptime(row.dimension_values[0].value, \"%Y%m%d\").date(),\n", "        \"sessions\": int(row.metric_values[0].value)\n", "    })\n", "\n", "# Convert to DataFrame\n", "daily_df = pd.DataFrame(daily_data)\n", "daily_df.sort_values(\"date\", inplace=True)\n", "daily_df.head()\n"]}, {"cell_type": "code", "execution_count": 16, "id": "be6e222b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib.dates as mdates\n", "\n", "# Set up the figure and plot\n", "plt.figure(figsize=(10, 5))\n", "plt.plot(daily_df['date'], daily_df['sessions'], marker='o', linestyle='-', color='blue', linewidth=2)\n", "\n", "# Format the x-axis to show dates nicely\n", "plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%b %d'))\n", "plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=3))  # Show every 3rd day\n", "\n", "# Add labels and title\n", "plt.title(\"Daily Sessions (Last 30 Days)\", fontsize=14)\n", "plt.xlabel(\"Date\", fontsize=12)\n", "plt.ylabel(\"Sessions\", fontsize=12)\n", "plt.grid(True)\n", "plt.tight_layout()\n", "\n", "# Save the plot as an image (for use in the PDF later)\n", "graph_path = \"daily_sessions_trend.png\"\n", "plt.savefig(graph_path)\n", "\n", "# Show the chart\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 20, "id": "604c6e67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DEBUG: Summary Data =  {'totalUsers': 6.0, 'newUsers': 6.0, 'sessions': 6.0, 'engagedSessions': 0.0, 'engagementRate': 0.0, 'averageSessionDuration': 160.38287383333335, 'eventCount': 23.0, 'screenPageViews': 6.0}\n", "✅ PDF saved to: GA_Analytics_Report.pdf\n"]}], "source": ["from reportlab.lib.pagesizes import A4\n", "from reportlab.pdfgen import canvas\n", "from reportlab.lib.units import inch\n", "from reportlab.lib import colors\n", "\n", "# Define the PDF path\n", "pdf_path = \"GA_Analytics_Report.pdf\"\n", "\n", "# Create the canvas\n", "c = canvas.Canvas(pdf_path, pagesize=A4)\n", "width, height = A4\n", "\n", "# Title\n", "<PERSON><PERSON>(\"Helvetica-Bold\", 18)\n", "c.drawString(1 * inch, height - 1 * inch, \"Monthly Google Analytics Report\")\n", "\n", "# Property info (you can customize this)\n", "<PERSON><PERSON><PERSON>(\"Helvetica\", 12)\n", "c.drawString(1 * inch, height - 1.3 * inch, f\"Property ID: {PROPERTY_ID}\")\n", "c.drawString(1 * inch, height - 1.55 * inch, f\"Date Range: {start_date} to {end_date}\")\n", "\n", "# Summary metrics section\n", "<PERSON><PERSON>(\"Helvetica-Bold\", 14)\n", "c.drawString(1 * inch, height - 2.1 * inch, \"Summary Metrics:\")\n", "print(\"DEBUG: Summary Data = \", summary_data)\n", "\n", "\n", "<PERSON><PERSON><PERSON>(\"Helvetica\", 12)\n", "c.drawString(1.2 * inch, height - 2.4 * inch, f\"👤 Total Users: {summary_data.get('totalUsers', 'N/A')}\")\n", "c.drawString(1.2 * inch, height - 2.7 * inch, f\"🆕 New Users: {summary_data.get('newUsers', 'N/A')}\")\n", "c.drawString(1.2 * inch, height - 3.0 * inch, f\"📈 Sessions: {summary_data.get('sessions', 'N/A')}\")\n", "c.drawString(1.2 * inch, height - 3.3 * inch, f\"✅ Engaged Sessions: {summary_data.get('engagedSessions', 'N/A')}\")\n", "c.drawString(1.2 * inch, height - 3.6 * inch, f\"📊 Engagement Rate: {summary_data.get('engagementRate', 'N/A')}\")\n", "c.drawString(1.2 * inch, height - 3.9 * inch, f\"⏱️ Avg Session Duration: {summary_data.get('averageSessionDuration', 'N/A'):.2f}s\")\n", "c.drawString(1.2 * inch, height - 4.2 * inch, f\"📌 Event Count: {summary_data.get('eventCount', 'N/A')}\")\n", "c.drawString(1.2 * inch, height - 4.5 * inch, f\"🖥️ Screen/Page Views: {summary_data.get('screenPageViews', 'N/A')}\")\n", "\n", "# Insert the line chart image\n", "chart_y = height - 6.5 * inch\n", "c.drawImage(graph_path, 1 * inch, chart_y, width=6 * inch, height=3 * inch)\n", "\n", "# Footer\n", "<PERSON><PERSON>(\"Helvetica-Oblique\", 9)\n", "c.<PERSON>ill<PERSON><PERSON>r(colors.grey)\n", "c.drawRightString(width - 1 * inch, 0.75 * inch, \"Generated by StreamzAI Analytics Dashboard\")\n", "\n", "# Save the PDF\n", "c.save()\n", "print(f\"✅ PDF saved to: {pdf_path}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "689d2157", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}