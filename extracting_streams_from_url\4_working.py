import os
import time
from io import BytesIO
from PIL import Image
from pptx import Presentation
from pptx.util import Inches, Pt
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# ========== CONFIG ========== #
HEADLESS = False
SCREENSHOTS_DIR = "screenshots"
PPT_FILE = "StreamContentSlides_1.pptx"
WAIT_TIMEOUT = 30
SCROLL_DELAY = 1.5

# ========== SETUP ========== #
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)
options = webdriver.ChromeOptions()
if HEADLESS:
    options.add_argument("--headless")
options.add_argument("--window-size=1920,1080")
driver = webdriver.Chrome(options=options)
wait = WebDriverWait(driver, WAIT_TIMEOUT)
driver.maximize_window()
# Zoom out to 80% to fit mobile preview in smaller viewports
driver.execute_script("document.body.style.zoom='80%'")
time.sleep(0.5)


input("➡️ Navigate to the stream edit page in Chrome, then press ENTER to begin...")

prs = Presentation()
blank_layout = prs.slide_layouts[6]

# ========== CAPTURE START ========== #
sections = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".section--info-card")))
print(f"✅ Found {len(sections)} sections.")

for sec_index in range(len(sections)):
    try:
        sections = driver.find_elements(By.CSS_SELECTOR, ".section--info-card")
        section = sections[sec_index]
        section_name = section.find_element(By.TAG_NAME, "h5").text.strip()
        safe_section_name = section_name.replace(" ", "_").replace("/", "-")
    except Exception as e:
        print(f"⚠️ Section {sec_index+1} skipped: {e}")
        continue

    print(f"\n🗂️ Section {sec_index+1}: {section_name}")
    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", section)
    time.sleep(SCROLL_DELAY)
    driver.execute_script("arguments[0].click();", section)
    time.sleep(SCROLL_DELAY * 2)

    for tab_type in ["Cards", "Assessment Questions"]:
        try:
            tab_xpath = f"//div[contains(@class, 'mat-tab-label') and contains(text(), '{tab_type}')]"
            tab = wait.until(EC.element_to_be_clickable((By.XPATH, tab_xpath)))
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", tab)
            driver.execute_script("arguments[0].click();", tab)
            time.sleep(SCROLL_DELAY * 2)

            cards = driver.find_elements(By.CSS_SELECTOR, "app-card div.info-card")
            print(f"  📄 {tab_type}: Found {len(cards)} cards.")

            card_images = []

            for card_index in range(len(cards)):
                try:
                    cards = driver.find_elements(By.CSS_SELECTOR, "app-card div.info-card")
                    card = cards[card_index]
                    card_title = card.text.strip().split("\n")[0].strip()
                    safe_card_title = "".join(c for c in card_title if c.isalnum() or c in (' ', '_', '-')).strip().replace(" ", "_")
                    filename = f"{safe_section_name}_{tab_type.replace(' ', '')}_{safe_card_title}.png"

                    print(f"    🔸 Capturing: {card_title}")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", card)
                    time.sleep(SCROLL_DELAY)
                    driver.execute_script("arguments[0].click();", card)
                    time.sleep(SCROLL_DELAY)

                    # preview = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".preview-card")))
                    # image = Image.open(BytesIO(preview.screenshot_as_png))
                    preview_card = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".preview-card")))
                    resize_ph = preview_card.find_element(By.CSS_SELECTOR, ".resize-ph")
                    iframe = resize_ph.find_element(By.CSS_SELECTOR, "iframe")

                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", resize_ph)
                    time.sleep(SCROLL_DELAY)

                    if card_index == 0:
                        print("⏳ Waiting extra time to stabilize first preview render...")
                        iframe = resize_ph.find_element(By.CSS_SELECTOR, "iframe")
                        driver.switch_to.frame(iframe)

                        # Wait until body has some height (content is rendered)
                        for _ in range(20):
                            body_height = driver.execute_script("return document.body.scrollHeight")
                            if body_height > 200:  # arbitrary threshold indicating content loaded
                                break
                            time.sleep(0.2)
                        driver.switch_to.default_content()
                        time.sleep(0.5)  # small buffer


                    element_width = preview_card.size['width']
                    element_height = preview_card.size['height']
                    driver.set_window_size(element_width + 200, element_height + 300)
                    time.sleep(SCROLL_DELAY)

                    driver.switch_to.frame(iframe)
                    scroll_height = driver.execute_script("return document.body.scrollHeight")
                    client_height = driver.execute_script("return document.documentElement.clientHeight")
                    driver.switch_to.default_content()

                    if scroll_height > client_height:
                        print(f"↕️ Scrolling needed for card {card_index+1} (scrollHeight: {scroll_height})")
                        stitched_images = []
                        scroll_y = 0
                        while scroll_y < scroll_height:
                            driver.switch_to.frame(iframe)
                            driver.execute_script(f"window.scrollTo(0, {scroll_y});")
                            time.sleep(0.8)
                            driver.switch_to.default_content()

                            image_part = Image.open(BytesIO(preview_card.screenshot_as_png))
                            stitched_images.append(image_part)
                            scroll_y += client_height

                        total_height = sum(img.height for img in stitched_images)
                        stitched = Image.new('RGB', (stitched_images[0].width, total_height))
                        offset = 0
                        for img in stitched_images:
                            stitched.paste(img, (0, offset))
                            offset += img.height

                        image = stitched
                    else:
                        image = Image.open(BytesIO(preview_card.screenshot_as_png))

                    
                    image.save(os.path.join(SCREENSHOTS_DIR, filename))
                    card_images.append((card_title, filename))
                    print(f"      📸 Saved: {filename}")
                except Exception as e:
                    print(f"⚠️ Failed to capture card {card_index+1}: {e}")
                    continue

            # === Add to PPT Slide: 2 cards per slide ===
            for i in range(0, len(card_images), 2):
                slide = prs.slides.add_slide(blank_layout)

                # Add section title
                left = Inches(0.5)
                top = Inches(0.2)
                width = Inches(9)
                height = Inches(0.5)
                title_box = slide.shapes.add_textbox(left, top, width, height)
                tf = title_box.text_frame
                p = tf.paragraphs[0]
                p.text = f"Section {sec_index+1} : {section_name}"
                p.font.bold = True
                p.font.size = Pt(20)

                for j in range(2):
                    if i + j >= len(card_images):
                        break
                    card_title, img_file = card_images[i + j]
                    img_path = os.path.join(SCREENSHOTS_DIR, img_file)
                    img = Image.open(img_path)
                    img_w, img_h = img.size
                    img_ratio = img_w / img_h

                    max_h = Inches(5.8)
                    new_h = max_h
                    new_w = new_h * img_ratio

                    pic_left = Inches(0.5) + j * (Inches(4.5))
                    pic_top = Inches(0.8)
                    slide.shapes.add_picture(img_path, pic_left, pic_top, height=new_h)

                    # Add label below
                    label_box = slide.shapes.add_textbox(pic_left, pic_top + new_h + Inches(0.1), Inches(4), Inches(0.4))
                    label_tf = label_box.text_frame
                    label_p = label_tf.paragraphs[0]
                    label_p.text = f"Card {i + j + 1}"
                    label_p.font.bold = True
                    label_p.font.size = Pt(16)

        except Exception as e:
            print(f"⚠️ Skipping tab '{tab_type}': {e}")
            continue

# ========== FINALIZE PPT ========== #
print("\n🧾 Saving PowerPoint...")
prs.save(PPT_FILE)
print(f"✅ Done! PPT created: {PPT_FILE}")

driver.quit()
