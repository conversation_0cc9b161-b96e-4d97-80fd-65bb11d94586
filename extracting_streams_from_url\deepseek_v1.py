import os
import time
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from io import BytesIO
from PIL import Image, ImageTk
import threading
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from pptx import Presentation
from pptx.util import Inches, Pt
import tkinter.messagebox as messagebox
from tkinter import Canvas
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("Warning: numpy not available, content analysis will be simplified")

class ScreenshotCaptureApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Screenshot Capture Tool")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # Initialize variables
        self.driver = None
        self.wait = None
        self.current_preview_image = None
        self.settings = {
            'scroll_delay': 1.5,
            'first_card_delay': 3.0,
            'wait_timeout': 30,
            'crop_area': None,  # Will store (x, y, width, height)
            'crop_area_relative': False,  # Flag to indicate relative coordinates
            'capture_mode': 'smart_preview'  # Default to smart preview mode
        }
        
        self.create_widgets()
        
    def create_widgets(self):
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # URL Input Section
        url_frame = ttk.LabelFrame(main_frame, text="URL Configuration", padding="10")
        url_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(url_frame, text="Enter URL:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.url_var = tk.StringVar()
        self.url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=60)
        self.url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        self.url_entry.bind('<Return>', self.open_browser)
        
        self.open_browser_btn = ttk.Button(url_frame, text="Open Browser", command=self.open_browser)
        self.open_browser_btn.grid(row=0, column=2, padx=(5, 0))
        
        url_frame.columnconfigure(1, weight=1)
        
        # Settings Section
        settings_frame = ttk.LabelFrame(main_frame, text="Capture Settings", padding="10")
        settings_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Scroll Delay setting
        ttk.Label(settings_frame, text="Scroll Delay (s):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.scroll_delay_var = tk.DoubleVar(value=self.settings['scroll_delay'])
        scroll_delay_spin = ttk.Spinbox(settings_frame, from_=0.5, to=5.0, increment=0.1, 
                                    textvariable=self.scroll_delay_var, width=10)
        scroll_delay_spin.grid(row=0, column=1, sticky=tk.W, pady=2)
        
        # First Card Delay setting
        ttk.Label(settings_frame, text="First Card Delay (s):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.first_card_delay_var = tk.DoubleVar(value=self.settings['first_card_delay'])
        first_card_delay_spin = ttk.Spinbox(settings_frame, from_=1.0, to=10.0, increment=0.5, 
                                        textvariable=self.first_card_delay_var, width=10)
        first_card_delay_spin.grid(row=1, column=1, sticky=tk.W, pady=2)
        
        # Capture Mode Selection
        ttk.Label(settings_frame, text="Capture Mode:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.capture_mode_var = tk.StringVar(value="smart_preview")
        capture_mode_combo = ttk.Combobox(settings_frame, textvariable=self.capture_mode_var,
                                        values=["smart_preview", "full_page_with_crop"],
                                        state="readonly", width=20)
        capture_mode_combo.grid(row=2, column=1, sticky=tk.W, pady=2)

        # Area Selection (for full page mode)
        ttk.Label(settings_frame, text="Crop Area:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.area_info_var = tk.StringVar(value="No area selected")
        area_info_label = ttk.Label(settings_frame, textvariable=self.area_info_var, foreground="blue")
        area_info_label.grid(row=3, column=1, sticky=tk.W, pady=2)

        self.select_area_btn = ttk.Button(settings_frame, text="Select Area",
                                        command=self.select_crop_area, state='disabled')
        self.select_area_btn.grid(row=4, column=0, pady=5)

        self.clear_area_btn = ttk.Button(settings_frame, text="Clear Area",
                                        command=self.clear_crop_area, state='disabled')
        self.clear_area_btn.grid(row=4, column=1, pady=5)

        # Info label for capture mode
        mode_info_label = ttk.Label(settings_frame,
                                  text="Smart Preview: Targets .resize-ph element directly\n"
                                       "Full Page + Crop: Uses crop area selection",
                                  foreground="gray", font=("TkDefaultFont", 8))
        mode_info_label.grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=2)
        
        settings_frame.columnconfigure(1, weight=1)
        
        # Preview Section
        preview_frame = ttk.LabelFrame(main_frame, text="Preview", padding="10")
        preview_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0), pady=(0, 10))
        
        # Preview canvas with scrollbar
        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.preview_canvas = tk.Canvas(canvas_frame, width=400, height=300, bg='white')
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.preview_canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.preview_canvas.xview)
        self.preview_canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        self.preview_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        canvas_frame.columnconfigure(0, weight=1)
        canvas_frame.rowconfigure(0, weight=1)
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        
        # Control Buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        self.test_screenshot_btn = ttk.Button(control_frame, text="Test Screenshot", 
                                             command=self.test_screenshot, state='disabled')
        self.test_screenshot_btn.grid(row=0, column=0, padx=5)
        
        self.apply_settings_btn = ttk.Button(control_frame, text="Apply Settings", 
                                           command=self.apply_settings, state='disabled')
        self.apply_settings_btn.grid(row=0, column=1, padx=5)
        
        self.export_all_btn = ttk.Button(control_frame, text="Export All Cards", 
                                        command=self.export_all_cards, state='disabled')
        self.export_all_btn.grid(row=0, column=2, padx=5)
        
        # Debug Test Button
        self.debug_test_btn = ttk.Button(control_frame, text="Debug Test", 
                                        command=self.debug_screenshot_test, state='disabled')
        self.debug_test_btn.grid(row=0, column=3, padx=5)
        
        # Test PPT Button
        self.test_ppt_btn = ttk.Button(control_frame, text="Test PPT", 
                                      command=lambda: self.create_test_slide("test_presentation.pptx"), 
                                      state='disabled')
        self.test_ppt_btn.grid(row=0, column=4, padx=5)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready - Enter URL and open browser to begin")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
    def open_browser(self, event=None):
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter a URL")
            return
            
        try:
            self.status_var.set("Opening browser...")
            self.setup_driver()
            self.driver.get(url)
            self.status_var.set("Browser opened. Navigate to the desired page and come back to test screenshot.")
            
            # Enable buttons
            self.test_screenshot_btn.config(state='normal')
            self.apply_settings_btn.config(state='normal')
            self.export_all_btn.config(state='normal')
            self.select_area_btn.config(state='normal')
            self.clear_area_btn.config(state='normal')
            self.debug_test_btn.config(state='normal')
            self.test_ppt_btn.config(state='normal')
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open browser: {str(e)}")
            self.status_var.set("Error opening browser")
            
    def setup_driver(self):
        if self.driver:
            self.driver.quit()
            
        options = webdriver.ChromeOptions()
        options.add_argument("--window-size=1920,1080")
        options.add_argument("--disable-web-security")
        options.add_argument("--disable-features=VizDisplayCompositor")
        
        self.driver = webdriver.Chrome(options=options)
        self.wait = WebDriverWait(self.driver, self.settings['wait_timeout'])
        self.driver.maximize_window()
        
    def apply_settings(self):
        if not self.driver:
            messagebox.showerror("Error", "No browser session active")
            return

        try:
            # Update settings
            self.settings['scroll_delay'] = self.scroll_delay_var.get()
            self.settings['first_card_delay'] = self.first_card_delay_var.get()
            self.settings['capture_mode'] = self.capture_mode_var.get()

            self.status_var.set("Settings applied")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply settings: {str(e)}")
            
    def select_crop_area(self):
        if not self.driver:
            messagebox.showerror("Error", "No browser session active")
            return
        
        try:
            # Get full page dimensions
            total_width = self.driver.execute_script("return document.body.scrollWidth")
            total_height = self.driver.execute_script("return document.body.scrollHeight")
            viewport_width = self.driver.execute_script("return window.innerWidth")
            viewport_height = self.driver.execute_script("return window.innerHeight")
            
            # Set window size to capture full page
            self.driver.set_window_size(total_width, total_height)
            time.sleep(1)  # Wait for resize
            
            # Take a full screenshot
            screenshot_bytes = self.driver.get_screenshot_as_png()
            full_image = Image.open(BytesIO(screenshot_bytes))
            
            # Restore original window size
            self.driver.set_window_size(viewport_width, viewport_height)
            
            # Create area selection window
            self.create_area_selection_window(full_image)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to capture screenshot for area selection: {str(e)}")

    def create_area_selection_window(self, full_image):
        # Create new window
        area_window = tk.Toplevel(self.root)
        area_window.title("Select Crop Area")
        area_window.geometry("1000x700")
        
        # Scale image to fit window
        display_scale = min(900/full_image.width, 600/full_image.height, 1.0)
        display_width = int(full_image.width * display_scale)
        display_height = int(full_image.height * display_scale)
        display_image = full_image.resize((display_width, display_height), Image.Resampling.LANCZOS)
        
        # Create canvas
        canvas = tk.Canvas(area_window, width=display_width, height=display_height, bg='white')
        canvas.pack(pady=10)
        
        # Display image
        photo = ImageTk.PhotoImage(display_image)
        canvas.create_image(0, 0, anchor=tk.NW, image=photo)
        
        # Variables for selection
        self.selection_start = None
        self.selection_rect = None
        self.selection_coords = None
        
        def on_mouse_down(event):
            self.selection_start = (event.x, event.y)
            if self.selection_rect:
                canvas.delete(self.selection_rect)
        
        def on_mouse_drag(event):
            if self.selection_start:
                if self.selection_rect:
                    canvas.delete(self.selection_rect)
                self.selection_rect = canvas.create_rectangle(
                    self.selection_start[0], self.selection_start[1],
                    event.x, event.y,
                    outline='red', width=2
                )
        
        def on_mouse_up(event):
            if self.selection_start:
                # Calculate selection coordinates (convert back to original image scale)
                x1 = int(min(self.selection_start[0], event.x) / display_scale)
                y1 = int(min(self.selection_start[1], event.y) / display_scale)
                x2 = int(max(self.selection_start[0], event.x) / display_scale)
                y2 = int(max(self.selection_start[1], event.y) / display_scale)
                
                self.selection_coords = (x1, y1, x2 - x1, y2 - y1)  # (x, y, width, height)
        
        def confirm_selection():
            if self.selection_coords:
                # Store both original coordinates and relative coordinates
                self.settings['crop_area'] = self.selection_coords
                self.settings['crop_area_relative'] = True  # Flag to indicate these are relative to full page
                x, y, w, h = self.selection_coords
                self.area_info_var.set(f"Area: {x},{y} ({w}x{h})")
                area_window.destroy()
                
                # Show warning about crop area usage
                messagebox.showinfo("Crop Area Set", 
                                  "Crop area selected. Note: This works best with full page screenshots.\n"
                                  "For element screenshots, cropping may be skipped if coordinates don't match.")
            else:
                messagebox.showwarning("Warning", "Please select an area first")
        
        def cancel_selection():
            area_window.destroy()
        
        # Bind mouse events
        canvas.bind("<Button-1>", on_mouse_down)
        canvas.bind("<B1-Motion>", on_mouse_drag)
        canvas.bind("<ButtonRelease-1>", on_mouse_up)
        
        # Buttons
        button_frame = ttk.Frame(area_window)
        button_frame.pack(pady=10)
        
        ttk.Button(button_frame, text="Confirm Selection", command=confirm_selection).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=cancel_selection).pack(side=tk.LEFT, padx=5)
        
        # Keep reference to photo
        area_window.photo = photo

    def clear_crop_area(self):
        self.settings['crop_area'] = None
        self.settings['crop_area_relative'] = False
        self.area_info_var.set("No area selected")

    # Improved apply_crop_area method
    def apply_crop_area(self, image):
        if self.settings['crop_area'] is None:
            return image
        
        x, y, width, height = self.settings['crop_area']
        
        # Ensure crop area is within image bounds
        x = max(0, min(x, image.width - 1))
        y = max(0, min(y, image.height - 1))
        width = min(width, image.width - x)
        height = min(height, image.height - y)
        
        # Add minimum size check
        if width > 10 and height > 10:  # Minimum 10x10 pixels
            try:
                cropped = image.crop((x, y, x + width, y + height))
                # Verify the cropped image has content
                if cropped.size[0] > 0 and cropped.size[1] > 0:
                    return cropped
            except Exception as e:
                print(f"Crop failed: {e}")
        
        return image

    def test_screenshot(self):
        if not self.driver:
            messagebox.showerror("Error", "No browser session active")
            return

        def capture_test():
            try:
                self.status_var.set("Capturing test screenshot...")
                self.root.update()

                # Apply current settings first
                self.apply_settings()

                # Check capture mode
                capture_mode = self.capture_mode_var.get()

                if capture_mode == "smart_preview":
                    # Try to find a preview card first (if available)
                    try:
                        container_info = self.get_preview_container_info()
                        if container_info:
                            self.status_var.set("Found preview container - using smart capture...")
                            self.root.update()

                            # Use smart capture for preview content
                            image = self.capture_preview_content_smart(container_info)

                            if image:
                                self.update_preview(image)
                                self.status_var.set("Test screenshot captured successfully (smart preview)")
                                return
                            else:
                                self.status_var.set("Smart preview capture failed, falling back to full page...")
                                self.root.update()

                    except Exception as e:
                        print(f"Smart preview capture attempt failed: {e}")
                        self.status_var.set("No preview found, falling back to full page...")
                        self.root.update()

                # Fallback to full page screenshot
                # Get full page dimensions
                total_width = self.driver.execute_script("return document.body.scrollWidth")
                total_height = self.driver.execute_script("return document.body.scrollHeight")
                viewport_width = self.driver.execute_script("return window.innerWidth")
                viewport_height = self.driver.execute_script("return window.innerHeight")

                # Set window size to capture full page
                self.driver.set_window_size(total_width, total_height)
                time.sleep(1)  # Wait for resize

                # Capture full webpage screenshot
                screenshot_bytes = self.driver.get_screenshot_as_png()
                image = Image.open(BytesIO(screenshot_bytes))

                # Restore original window size
                self.driver.set_window_size(viewport_width, viewport_height)

                # Apply crop area if selected
                if self.settings['crop_area'] is not None:
                    image = self.apply_crop_area(image)

                # Update preview
                self.update_preview(image)
                self.status_var.set("Test screenshot captured successfully (full page)")

            except Exception as e:
                self.status_var.set(f"Error: {str(e)}")
                messagebox.showerror("Error", str(e))

        # Run in separate thread to avoid blocking UI
        threading.Thread(target=capture_test, daemon=True).start()
        
    def update_preview(self, image):
        # Resize image to fit preview canvas
        canvas_width = self.preview_canvas.winfo_width()
        canvas_height = self.preview_canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            canvas_width, canvas_height = 400, 300
            
        # Calculate scaling to fit in canvas
        scale_x = canvas_width / image.width
        scale_y = canvas_height / image.height
        scale = min(scale_x, scale_y, 1.0)  # Don't scale up
        
        if scale < 1.0:
            new_width = int(image.width * scale)
            new_height = int(image.height * scale)
            display_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        else:
            display_image = image
            
        # Convert to PhotoImage
        self.current_preview_image = ImageTk.PhotoImage(display_image)
        
        # Clear canvas and add image
        self.preview_canvas.delete("all")
        self.preview_canvas.create_image(
            canvas_width // 2, canvas_height // 2,
            anchor=tk.CENTER, image=self.current_preview_image
        )
        
        # Update scroll region
        self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))
        
    def wait_for_preview_content_loaded(self, iframe_element, timeout=10):
        """Wait for preview content to be fully loaded"""
        try:
            self.driver.switch_to.frame(iframe_element)
            WebDriverWait(self.driver, timeout).until(
                lambda d: d.execute_script("return document.readyState") == 'complete'
            )
            # Additional wait for any dynamic content
            time.sleep(1)
            self.driver.switch_to.default_content()
            return True
        except Exception as e:
            print(f"Error waiting for content: {e}")
            self.driver.switch_to.default_content()
            return False

    def get_preview_container_info(self):
        """Get detailed information about the preview container"""
        try:
            preview_card = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".preview-card")))
            resize_ph = preview_card.find_element(By.CSS_SELECTOR, ".resize-ph")
            iframe = resize_ph.find_element(By.CSS_SELECTOR, "iframe")

            # Get container dimensions
            container_location = resize_ph.location
            container_size = resize_ph.size

            # Get iframe content dimensions
            self.driver.switch_to.frame(iframe)
            scroll_height = self.driver.execute_script("return Math.max(document.body.scrollHeight, document.documentElement.scrollHeight)")
            client_height = self.driver.execute_script("return document.documentElement.clientHeight")
            scroll_width = self.driver.execute_script("return Math.max(document.body.scrollWidth, document.documentElement.scrollWidth)")
            client_width = self.driver.execute_script("return document.documentElement.clientWidth")
            self.driver.switch_to.default_content()

            return {
                'preview_card': preview_card,
                'resize_ph': resize_ph,
                'iframe': iframe,
                'container_location': container_location,
                'container_size': container_size,
                'scroll_height': scroll_height,
                'client_height': client_height,
                'scroll_width': scroll_width,
                'client_width': client_width,
                'is_scrollable_vertical': scroll_height > client_height,
                'is_scrollable_horizontal': scroll_width > client_width
            }
        except Exception as e:
            print(f"Error getting preview container info: {e}")
            return None

    def capture_preview_element_direct(self, container_info):
        """Capture the preview element directly without crop overlays"""
        try:
            resize_ph = container_info['resize_ph']
            iframe = container_info['iframe']

            # Scroll element into view
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", resize_ph)
            time.sleep(0.5)

            # Try multiple capture methods
            methods = [
                ("iframe_element", lambda: iframe.screenshot_as_png),
                ("resize_ph_element", lambda: resize_ph.screenshot_as_png),
                ("full_page_crop", self._capture_with_full_page_crop)
            ]

            for method_name, capture_func in methods:
                try:
                    print(f"Trying capture method: {method_name}")

                    if method_name == "full_page_crop":
                        image = capture_func(container_info)
                    else:
                        screenshot_bytes = capture_func()
                        image = Image.open(BytesIO(screenshot_bytes))

                    if image and image.size[0] > 50 and image.size[1] > 50:
                        # Check if image has actual content (not just blank)
                        if self._has_meaningful_content(image):
                            print(f"✓ {method_name} capture successful: {image.size}")
                            return image
                        else:
                            print(f"✗ {method_name} captured blank content")
                    else:
                        print(f"✗ {method_name} captured too small: {image.size if image else 'None'}")

                except Exception as e:
                    print(f"✗ {method_name} failed: {e}")
                    continue

            print("All direct capture methods failed")
            return None

        except Exception as e:
            print(f"Direct element capture failed: {e}")
            return None

    def _has_meaningful_content(self, image):
        """Check if image has meaningful content (not just blank/empty)"""
        try:
            if NUMPY_AVAILABLE:
                # Convert to grayscale and check variance
                gray = image.convert('L')
                pixels = np.array(gray)
                variance = np.var(pixels)

                # If variance is very low, it's likely a blank image
                has_content = variance > 100  # Threshold for meaningful content
                print(f"Image variance: {variance:.2f}, has content: {has_content}")
                return has_content
            else:
                # Simplified check without numpy
                gray = image.convert('L')
                # Get a sample of pixels and check if they're all similar
                width, height = gray.size
                sample_pixels = []
                for i in range(0, width, max(1, width//10)):
                    for j in range(0, height, max(1, height//10)):
                        sample_pixels.append(gray.getpixel((i, j)))

                # Check if all pixels are very similar (indicating blank image)
                if len(sample_pixels) > 1:
                    pixel_range = max(sample_pixels) - min(sample_pixels)
                    has_content = pixel_range > 30  # Threshold for meaningful variation
                    print(f"Pixel range: {pixel_range}, has content: {has_content}")
                    return has_content

                return True  # Default to assuming content

        except Exception as e:
            print(f"Content check failed: {e}")
            # If we can't check, assume it has content
            return True

    def _capture_with_full_page_crop(self, container_info):
        """Fallback method using full page screenshot with element cropping"""
        try:
            resize_ph = container_info['resize_ph']

            # Get element position and size
            element_location = resize_ph.location
            element_size = resize_ph.size

            print(f"Element location: {element_location}, size: {element_size}")

            # Take full page screenshot
            screenshot_bytes = self.driver.get_screenshot_as_png()
            full_image = Image.open(BytesIO(screenshot_bytes))

            # Crop to element area with some padding
            padding = 10
            left = max(0, element_location['x'] - padding)
            top = max(0, element_location['y'] - padding)
            right = min(full_image.width, element_location['x'] + element_size['width'] + padding)
            bottom = min(full_image.height, element_location['y'] + element_size['height'] + padding)

            cropped_image = full_image.crop((left, top, right, bottom))
            print(f"Full page crop result: {cropped_image.size}")

            return cropped_image

        except Exception as e:
            print(f"Full page crop failed: {e}")
            return None

    def capture_iframe_content_directly(self, container_info):
        """Capture the iframe content directly by switching context"""
        try:
            iframe = container_info['iframe']

            # Switch to iframe context
            self.driver.switch_to.frame(iframe)

            # Wait for content to load
            WebDriverWait(self.driver, 10).until(
                lambda d: d.execute_script("return document.readyState") == 'complete'
            )

            # Get iframe body dimensions
            body_width = self.driver.execute_script("return document.body.scrollWidth")
            body_height = self.driver.execute_script("return document.body.scrollHeight")

            print(f"Iframe content dimensions: {body_width}x{body_height}")

            # Take screenshot of the iframe content
            screenshot_bytes = self.driver.get_screenshot_as_png()
            self.driver.switch_to.default_content()

            # The screenshot will be of the entire page, but the iframe content
            # should be visible. We need to crop to the iframe area.
            full_image = Image.open(BytesIO(screenshot_bytes))

            # Get iframe position in the main page
            iframe_location = iframe.location
            iframe_size = iframe.size

            # Crop to iframe area
            left = iframe_location['x']
            top = iframe_location['y']
            right = left + iframe_size['width']
            bottom = top + iframe_size['height']

            iframe_image = full_image.crop((left, top, right, bottom))

            print(f"Iframe capture result: {iframe_image.size}")
            return iframe_image

        except Exception as e:
            print(f"Iframe content capture failed: {e}")
            self.driver.switch_to.default_content()  # Ensure we're back to main context
            return None

    def capture_mobile_preview_smart(self, container_info):
        """Smart method specifically for mobile preview capture"""
        try:
            # Method 1: Try iframe content directly
            print("=== Trying iframe content capture ===")
            image = self.capture_iframe_content_directly(container_info)
            if image and self._has_meaningful_content(image):
                return image

            # Method 2: Try iframe element screenshot
            print("=== Trying iframe element screenshot ===")
            try:
                iframe = container_info['iframe']
                screenshot_bytes = iframe.screenshot_as_png
                image = Image.open(BytesIO(screenshot_bytes))
                if image and self._has_meaningful_content(image):
                    return image
            except Exception as e:
                print(f"Iframe element screenshot failed: {e}")

            # Method 3: Try the enhanced direct capture
            print("=== Trying enhanced direct capture ===")
            image = self.capture_preview_element_direct(container_info)
            if image and self._has_meaningful_content(image):
                return image

            print("All mobile preview capture methods failed")
            return None

        except Exception as e:
            print(f"Smart mobile preview capture failed: {e}")
            return None

    # Debug method for screenshot testing
    def debug_screenshot_test(self):
        """Enhanced debug method to test improved screenshot capture"""
        if not self.driver:
            messagebox.showerror("Error", "No browser session active")
            return

        try:
            # Find a card to test
            cards = self.driver.find_elements(By.CSS_SELECTOR, "app-card div.info-card")
            if not cards:
                messagebox.showerror("Error", "No cards found")
                return

            card = cards[0]
            self.driver.execute_script("arguments[0].click();", card)
            time.sleep(2)

            # Get comprehensive preview container information
            container_info = self.get_preview_container_info()
            if not container_info:
                messagebox.showerror("Error", "Could not get preview container info")
                return

            print("=== Preview Container Analysis ===")
            print(f"Container location: {container_info['container_location']}")
            print(f"Container size: {container_info['container_size']}")
            print(f"Content scroll height: {container_info['scroll_height']}")
            print(f"Content client height: {container_info['client_height']}")
            print(f"Is vertically scrollable: {container_info['is_scrollable_vertical']}")
            print(f"Is horizontally scrollable: {container_info['is_scrollable_horizontal']}")

            # Wait for content to load
            self.wait_for_preview_content_loaded(container_info['iframe'])

            # Test Method 1: Mobile Preview Smart Capture
            print("\n=== Testing Mobile Preview Smart Capture ===")
            image1 = self.capture_mobile_preview_smart(container_info)
            if image1:
                image1.save("debug_mobile_preview_smart.png")
                print("✓ Mobile preview smart capture successful")

            # Test Method 2: Direct element capture
            print("\n=== Testing Direct Element Capture ===")
            image2 = self.capture_preview_element_direct(container_info)
            if image2:
                image2.save("debug_direct_element.png")
                print("✓ Direct element capture successful")

            # Test Method 3: Enhanced scrollable content capture
            if container_info['is_scrollable_vertical']:
                print("\n=== Testing Scrollable Content Capture ===")
                image3 = self.capture_scrollable_preview_content(container_info)
                if image3:
                    image3.save("debug_scrollable_stitched.png")
                    print("✓ Scrollable content capture successful")

            debug_files = ["- debug_mobile_preview_smart.png", "- debug_direct_element.png"]
            if container_info['is_scrollable_vertical']:
                debug_files.append("- debug_scrollable_stitched.png")

            messagebox.showinfo("Debug Complete",
                              f"Debug screenshots saved:\n" +
                              "\n".join(debug_files) +
                              f"\n\nCheck console for detailed analysis.")

        except Exception as e:
            messagebox.showerror("Error", f"Debug test failed: {str(e)}")

    def capture_scrollable_preview_content(self, container_info):
        """Capture scrollable content by stitching multiple screenshots"""
        try:
            iframe = container_info['iframe']
            resize_ph = container_info['resize_ph']
            scroll_height = container_info['scroll_height']
            client_height = container_info['client_height']

            if not container_info['is_scrollable_vertical']:
                # Not scrollable, just capture normally
                return self.capture_preview_element_direct(container_info)

            print(f"Capturing scrollable content: {scroll_height}px total, {client_height}px viewport")

            stitched_images = []
            scroll_y = 0

            while scroll_y < scroll_height:
                # Scroll to position
                self.driver.switch_to.frame(iframe)
                self.driver.execute_script(f"window.scrollTo(0, {scroll_y});")
                time.sleep(0.8)  # Wait for scroll to settle
                self.driver.switch_to.default_content()

                # Capture the preview container at this scroll position
                screenshot_bytes = resize_ph.screenshot_as_png
                image_part = Image.open(BytesIO(screenshot_bytes))
                stitched_images.append(image_part)

                scroll_y += client_height

                # Prevent infinite loop
                if len(stitched_images) > 20:  # Max 20 segments
                    print("Warning: Too many scroll segments, stopping")
                    break

            if not stitched_images:
                return None

            # Stitch images together
            total_height = sum(img.height for img in stitched_images)
            stitched = Image.new('RGB', (stitched_images[0].width, total_height))

            offset = 0
            for img in stitched_images:
                stitched.paste(img, (0, offset))
                offset += img.height

            print(f"Stitched image size: {stitched.size}")
            return stitched

        except Exception as e:
            print(f"Scrollable content capture failed: {e}")
            return None

    def capture_preview_content_smart(self, container_info):
        """Smart capture that chooses the best method based on content"""
        try:
            # Wait for content to load
            self.wait_for_preview_content_loaded(container_info['iframe'])

            # First try the mobile preview specific method
            print("=== Using Mobile Preview Smart Capture ===")
            image = self.capture_mobile_preview_smart(container_info)

            if image and self._has_meaningful_content(image):
                # Check if we need to handle scrollable content
                if container_info['is_scrollable_vertical']:
                    print("Content is scrollable - checking if we need stitching")
                    # If the captured image height is much smaller than scroll height,
                    # we might need stitching
                    scroll_ratio = container_info['scroll_height'] / container_info['client_height']
                    if scroll_ratio > 1.5:  # Significant scrollable content
                        print("Attempting scrollable content stitching")
                        stitched_image = self.capture_scrollable_preview_content(container_info)
                        if stitched_image and stitched_image.height > image.height:
                            return stitched_image

                return image

            # Fallback to original methods
            if container_info['is_scrollable_vertical']:
                print("Fallback: Content is scrollable - using stitching method")
                return self.capture_scrollable_preview_content(container_info)
            else:
                print("Fallback: Content is not scrollable - using direct capture")
                return self.capture_preview_element_direct(container_info)

        except Exception as e:
            print(f"Smart capture failed: {e}")
            return None

    # CHANGE 4: Create test slide for debugging
    def create_test_slide(self, save_path):
        """Create a test slide to verify image embedding works"""
        try:
            # Create a simple test
            prs = Presentation()
            slide = prs.slides.add_slide(prs.slide_layouts[6])
            
            # Add title
            title_box = slide.shapes.add_textbox(Inches(1), Inches(1), Inches(8), Inches(1))
            title_box.text_frame.text = "Test Slide - Image Embedding Test"
            
            # Take a simple screenshot
            screenshot_bytes = self.driver.get_screenshot_as_png()
            image = Image.open(BytesIO(screenshot_bytes))
            
            # Convert to RGB
            if image.mode in ('RGBA', 'LA', 'P'):
                rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                rgb_image.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = rgb_image
            
            # Save test image
            screenshots_dir = "screenshots"
            os.makedirs(screenshots_dir, exist_ok=True)
            test_img_path = os.path.join(screenshots_dir, "test_image.png")
            image.save(test_img_path, 'PNG', optimize=False, quality=100)
            
            # Add to slide
            abs_path = os.path.abspath(test_img_path)
            picture = slide.shapes.add_picture(abs_path, Inches(2), Inches(2))
            picture.width = Inches(6)
            picture.height = int(Inches(6).inches * image.height / image.width * 914400)
            
            # Save PowerPoint
            test_ppt_path = save_path.replace('.pptx', '_test.pptx')
            prs.save(test_ppt_path)
            
            messagebox.showinfo("Test Complete", f"Test slide saved to: {test_ppt_path}")
            
        except Exception as e:
            messagebox.showerror("Test Failed", f"Test slide creation failed: {str(e)}")
        
    def export_all_cards(self):
        if not self.driver:
            messagebox.showerror("Error", "No browser session active")
            return
            
        # Ask for save location
        save_path = filedialog.asksaveasfilename(
            defaultextension=".pptx",
            filetypes=[("PowerPoint files", "*.pptx"), ("All files", "*.*")],
            title="Save PowerPoint Presentation"
        )
        
        if not save_path:
            return
            
        def export_process():
            try:
                self.status_var.set("Starting export process...")
                self.root.update()
                
                # Apply current settings
                self.apply_settings()
                
                # Create screenshots directory
                screenshots_dir = "screenshots"
                os.makedirs(screenshots_dir, exist_ok=True)
                
                # Create PowerPoint presentation
                prs = Presentation()
                blank_layout = prs.slide_layouts[6]
                
                # Find all sections
                sections = self.wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".section--info-card")))
                total_sections = len(sections)
                
                self.status_var.set(f"Found {total_sections} sections to process...")
                self.root.update()
                
                for sec_index in range(total_sections):
                    try:
                        sections = self.driver.find_elements(By.CSS_SELECTOR, ".section--info-card")
                        section = sections[sec_index]
                        section_name = section.find_element(By.TAG_NAME, "h5").text.strip()
                        safe_section_name = section_name.replace(" ", "_").replace("/", "-")
                        
                        self.status_var.set(f"Processing section {sec_index+1}/{total_sections}: {section_name}")
                        self.root.update()
                        
                        # Click section
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", section)
                        time.sleep(self.settings['scroll_delay'])
                        self.driver.execute_script("arguments[0].click();", section)
                        time.sleep(self.settings['scroll_delay'] * 2)
                        
                        # Process each tab type
                        for tab_type in ["Cards", "Assessment Questions"]:
                            try:
                                tab_xpath = f"//div[contains(@class, 'mat-tab-label') and contains(text(), '{tab_type}')]"
                                tab = self.wait.until(EC.element_to_be_clickable((By.XPATH, tab_xpath)))
                                self.driver.execute_script("arguments[0].click();", tab)
                                time.sleep(self.settings['scroll_delay'] * 2)
                                
                                cards = self.driver.find_elements(By.CSS_SELECTOR, "app-card div.info-card")
                                card_images = []
                                
                                for card_index in range(len(cards)):
                                    try:
                                        cards = self.driver.find_elements(By.CSS_SELECTOR, "app-card div.info-card")
                                        card = cards[card_index]
                                        card_title = card.text.strip().split("\n")[0].strip()
                                        safe_card_title = "".join(c for c in card_title if c.isalnum() or c in (' ', '_', '-')).strip().replace(" ", "_")
                                        filename = f"{safe_section_name}_{tab_type.replace(' ', '')}_{safe_card_title}.png"
                                        
                                        self.status_var.set(f"Capturing card {card_index+1}/{len(cards)}: {card_title}")
                                        self.root.update()
                                        
                                        # Click card
                                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", card)
                                        time.sleep(self.settings['scroll_delay'])
                                        self.driver.execute_script("arguments[0].click();", card)
                                        time.sleep(self.settings['scroll_delay'])
                                        
                                        # Enhanced screenshot capture using new smart methods
                                        try:
                                            capture_mode = self.settings.get('capture_mode', 'smart_preview')

                                            if capture_mode == "smart_preview":
                                                # Get comprehensive container information
                                                container_info = self.get_preview_container_info()
                                                if not container_info:
                                                    print("Could not get preview container info, skipping card")
                                                    continue

                                                # Special handling for first card
                                                if card_index == 0:
                                                    time.sleep(self.settings['first_card_delay'])
                                                else:
                                                    time.sleep(self.settings['scroll_delay'])

                                                # Use smart capture method
                                                image = self.capture_preview_content_smart(container_info)

                                                if image is None:
                                                    print("Smart capture failed, trying fallback method")
                                                    # Fallback to direct element capture
                                                    image = self.capture_preview_element_direct(container_info)

                                                if image is None:
                                                    print("All capture methods failed, skipping card")
                                                    continue

                                            else:  # full_page_with_crop mode
                                                # Use legacy method with crop area
                                                preview_card = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".preview-card")))
                                                resize_ph = preview_card.find_element(By.CSS_SELECTOR, ".resize-ph")
                                                iframe = resize_ph.find_element(By.CSS_SELECTOR, "iframe")

                                                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", resize_ph)

                                                # Special handling for first card
                                                if card_index == 0:
                                                    time.sleep(self.settings['first_card_delay'])
                                                    # Ensure content is loaded
                                                    self.driver.switch_to.frame(iframe)
                                                    WebDriverWait(self.driver, 10).until(
                                                        lambda d: d.execute_script("return document.readyState") == 'complete'
                                                    )
                                                    self.driver.switch_to.default_content()
                                                else:
                                                    time.sleep(self.settings['scroll_delay'])

                                                # Take full page screenshot and crop
                                                screenshot_bytes = self.driver.get_screenshot_as_png()
                                                full_image = Image.open(BytesIO(screenshot_bytes))

                                                # Get element position and crop
                                                element_location = resize_ph.location
                                                element_size = resize_ph.size
                                                left = element_location['x']
                                                top = element_location['y']
                                                right = left + element_size['width']
                                                bottom = top + element_size['height']

                                                image = full_image.crop((left, top, right, bottom))

                                                # Apply crop area if selected
                                                if self.settings['crop_area'] is not None:
                                                    image = self.apply_crop_area(image)

                                            # Verify image has content
                                            if image.size[0] == 0 or image.size[1] == 0:
                                                print("Captured image is empty, skipping card")
                                                continue

                                        except Exception as e:
                                            print(f"Enhanced screenshot capture failed: {e}")
                                            continue  # Skip this card
                                        
                                        # CHANGE 1: Improved image saving
                                        # Save image with better format handling
                                        image_path = os.path.join(screenshots_dir, filename)
                                        try:
                                            # Convert to RGB if necessary (removes alpha channel issues)
                                            if image.mode in ('RGBA', 'LA', 'P'):
                                                rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                                                if image.mode == 'P':
                                                    image = image.convert('RGBA')
                                                rgb_image.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                                                image = rgb_image
                                            
                                            # Save as PNG with high quality
                                            image.save(image_path, 'PNG', optimize=False, quality=100)
                                            
                                            # Verify the saved image
                                            test_img = Image.open(image_path)
                                            if test_img.size[0] == 0 or test_img.size[1] == 0:
                                                raise Exception("Saved image is empty")
                                            test_img.close()
                                            
                                            card_images.append((card_title, filename, image_path))  # Store full path
                                            print(f"Successfully saved: {filename} ({image.size})")
                                            
                                        except Exception as e:
                                            print(f"Failed to save image {filename}: {e}")
                                            continue
                                        
                                    except Exception as e:
                                        print(f"Failed to capture card {card_index+1}: {e}")
                                        continue
                                
                                # CHANGE 3: Updated card_images list processing
                                scrollable_cards = []
                                non_scrollable_cards = []
                                
                                for card_title, img_file, img_path in card_images:
                                    try:
                                        if not os.path.exists(img_path):
                                            print(f"Image file not found: {img_path}")
                                            continue
                                            
                                        img = Image.open(img_path)
                                        if img.size[0] == 0 or img.size[1] == 0:
                                            print(f"Empty image: {img_path}")
                                            continue
                                            
                                        # Check if image is taller than it is wide (likely scrollable content)
                                        if img.height > img.width * 1.2:  # Threshold for scrollable content
                                            scrollable_cards.append((card_title, img_file, img_path))
                                        else:
                                            non_scrollable_cards.append((card_title, img_file, img_path))
                                        img.close()
                                        
                                    except Exception as e:
                                        print(f"Failed to analyze card {img_file}: {e}")
                                        continue
                                
                                # Create slides based on card types
                                all_cards = []
                                
                                # Add scrollable cards (4 per slide)
                                for i in range(0, len(scrollable_cards), 4):
                                    slide_cards = scrollable_cards[i:i+4]
                                    all_cards.append(('scrollable', slide_cards))
                                
                                # Add non-scrollable cards (2 per slide)
                                for i in range(0, len(non_scrollable_cards), 2):
                                    slide_cards = non_scrollable_cards[i:i+2]
                                    all_cards.append(('non_scrollable', slide_cards))
                                
                                # Create PowerPoint slides
                                for slide_type, slide_cards in all_cards:
                                    slide = prs.slides.add_slide(blank_layout)
                                    
                                    # Add title
                                    left = Inches(0.5)
                                    top = Inches(0.2)
                                    width = Inches(9)
                                    height = Inches(0.5)
                                    title_box = slide.shapes.add_textbox(left, top, width, height)
                                    tf = title_box.text_frame
                                    p = tf.paragraphs[0]
                                    p.text = f"Section {sec_index+1}: {section_name} - {tab_type}"
                                    p.font.bold = True
                                    
                                    if slide_type == 'scrollable':
                                        # 4 images in 2x2 grid for scrollable content
                                        p.font.size = Pt(16)
                                        
                                        # CHANGE 2: Updated slide creation for scrollable cards
                                        for j, (card_title, img_file, img_path) in enumerate(slide_cards):
                                            try:
                                                # Calculate position (2x2 grid)
                                                row = j // 2
                                                col = j % 2
                                                pic_left = Inches(0.5) + col * Inches(4.5)
                                                pic_top = Inches(0.8) + row * Inches(3.5)
                                                
                                                # Verify image exists and is readable
                                                if not os.path.exists(img_path):
                                                    print(f"Image not found: {img_path}")
                                                    continue
                                                    
                                                try:
                                                    # Test if image can be opened
                                                    test_img = Image.open(img_path)
                                                    if test_img.size[0] == 0 or test_img.size[1] == 0:
                                                        print(f"Image is empty: {img_path}")
                                                        continue
                                                    img_width, img_height = test_img.size
                                                    test_img.close()
                                                except Exception as e:
                                                    print(f"Cannot open image {img_path}: {e}")
                                                    continue
                                                
                                                # Add image to slide
                                                abs_img_path = os.path.abspath(img_path)
                                                picture = slide.shapes.add_picture(abs_img_path, pic_left, pic_top)
                                                
                                                # Calculate and set dimensions
                                                max_w = Inches(2.0)
                                                max_h = Inches(2.8)
                                                img_ratio = img_width / img_height
                                                
                                                if img_ratio > (max_w.inches / max_h.inches):
                                                    # Image is wider - fit to width
                                                    picture.width = max_w
                                                    picture.height = int(max_w.inches * img_height / img_width * 914400)
                                                else:
                                                    # Image is taller - fit to height
                                                    picture.height = max_h
                                                    picture.width = int(max_h.inches * img_width / img_height * 914400)
                                                
                                                # Reposition after resize
                                                picture.left = pic_left
                                                picture.top = pic_top
                                                
                                                # Add label
                                                label_box = slide.shapes.add_textbox(
                                                    pic_left, pic_top + picture.height + Inches(0.05), 
                                                    Inches(4), Inches(0.3)
                                                )
                                                label_tf = label_box.text_frame
                                                label_p = label_tf.paragraphs[0]
                                                label_p.text = f"{j + 1}. {card_title[:20]}{'...' if len(card_title) > 20 else ''}"
                                                label_p.font.size = Pt(10)
                                                
                                                print(f"Added image to slide: {img_file}")
                                                
                                            except Exception as e:
                                                print(f"Failed to add image to slide: {img_file}, error: {e}")
                                                continue
                                    
                                    else:
                                        # 2 images side by side for non-scrollable content
                                        p.font.size = Pt(20)
                                        
                                        # CHANGE 2: Updated slide creation for non-scrollable cards
                                        for j, (card_title, img_file, img_path) in enumerate(slide_cards):
                                            try:
                                                pic_left = Inches(0.5) + j * Inches(4.5)
                                                pic_top = Inches(0.8)
                                                
                                                # Verify image exists and is readable
                                                if not os.path.exists(img_path):
                                                    print(f"Image not found: {img_path}")
                                                    continue
                                                    
                                                try:
                                                    # Test if image can be opened
                                                    test_img = Image.open(img_path)
                                                    if test_img.size[0] == 0 or test_img.size[1] == 0:
                                                        print(f"Image is empty: {img_path}")
                                                        continue
                                                    img_width, img_height = test_img.size
                                                    test_img.close()
                                                except Exception as e:
                                                    print(f"Cannot open image {img_path}: {e}")
                                                    continue
                                                
                                                # Add image to slide
                                                abs_img_path = os.path.abspath(img_path)
                                                picture = slide.shapes.add_picture(abs_img_path, pic_left, pic_top)
                                                
                                                # Calculate and set dimensions
                                                max_w = Inches(4.2)
                                                max_h = Inches(5.8)
                                                img_ratio = img_width / img_height
                                                
                                                if img_ratio > (max_w.inches / max_h.inches):
                                                    # Image is wider - fit to width
                                                    picture.width = max_w
                                                    picture.height = int(max_w.inches * img_height / img_width * 914400)
                                                else:
                                                    # Image is taller - fit to height
                                                    picture.height = max_h
                                                    picture.width = int(max_h.inches * img_width / img_height * 914400)
                                                
                                                # Reposition after resize
                                                picture.left = pic_left
                                                picture.top = pic_top
                                                
                                                # Add label
                                                label_box = slide.shapes.add_textbox(
                                                    pic_left, pic_top + picture.height + Inches(0.1), 
                                                    Inches(4), Inches(0.4)
                                                )
                                                label_tf = label_box.text_frame
                                                label_p = label_tf.paragraphs[0]
                                                label_p.text = f"{j + 1}. {card_title[:25]}{'...' if len(card_title) > 25 else ''}"
                                                label_p.font.size = Pt(12)
                                                
                                                print(f"Added image to slide: {img_file}")
                                                
                                            except Exception as e:
                                                print(f"Failed to add image to slide: {img_file}, error: {e}")
                                                continue
                                
                            except Exception as e:
                                print(f"Failed to process tab {tab_type}: {e}")
                                continue
                        
                    except Exception as e:
                        print(f"Failed to process section {sec_index+1}: {e}")
                        continue
                
                # Save PowerPoint
                self.status_var.set("Saving PowerPoint presentation...")
                self.root.update()
                prs.save(save_path)
                
                self.status_var.set(f"Export completed successfully! Saved to: {save_path}")
                messagebox.showinfo("Success", f"Export completed successfully!\nSaved to: {save_path}")
                
            except Exception as e:
                self.status_var.set(f"Export failed: {str(e)}")
                messagebox.showerror("Error", f"Export failed: {str(e)}")
        
        # Run export in separate thread
        threading.Thread(target=export_process, daemon=True).start()
    
    def on_closing(self):
        if self.driver:
            self.driver.quit()
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = ScreenshotCaptureApp(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()