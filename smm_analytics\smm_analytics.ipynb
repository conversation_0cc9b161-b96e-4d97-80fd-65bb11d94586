{"cells": [{"cell_type": "markdown", "id": "1e4aabea", "metadata": {}, "source": ["# Social Media Analytics\n", "\n", "code snippets of fb, ig\n", "1. content or post retreival \n", "2. analytics data presentation and retrieval\n", "3. publish "]}, {"cell_type": "code", "execution_count": 2, "id": "e3f9ec61", "metadata": {}, "outputs": [], "source": ["# Variables initialisation\n", "\n", "# env_loader.py\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "\n", "def load_env():\n", "    load_dotenv()\n", "    env_vars = {\n", "        \"ACCESS_TOKEN\": os.getenv(\"ACCESS_TOKEN\"),\n", "        \"FB_PAGE_ID\": os.getenv(\"FB_PAGE_ID\"),\n", "        \"IG_USER_ID\": os.getenv(\"IG_USER_ID\"),\n", "        \"FB_PAGE_ACCESS_TOKEN\": os.getenv(\"FB_PAGE_ACCESS_TOKEN\")\n", "    }\n", "    missing = [key for key, val in env_vars.items() if val is None]\n", "    if missing:\n", "        raise EnvironmentError(f\"Missing env variables: {', '.join(missing)}\")\n", "    return env_vars\n"]}, {"cell_type": "code", "execution_count": 3, "id": "9f15375a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>message</th>\n", "      <th>created_time</th>\n", "      <th>permalink_url</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>143859782330858_1070505058477192</td>\n", "      <td>To our valued customers — we’re grateful for t...</td>\n", "      <td>2025-07-02T16:30:09+0000</td>\n", "      <td>https://www.facebook.com/****************/post...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>143859782330858_1069041158623582</td>\n", "      <td>It was a great venue to have the Peoria Chambe...</td>\n", "      <td>2025-06-30T20:42:00+0000</td>\n", "      <td>https://www.facebook.com/****************/post...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>143859782330858_****************</td>\n", "      <td>💬 Got questions about insurance? We’re all ear...</td>\n", "      <td>2025-06-30T16:30:04+0000</td>\n", "      <td>https://www.facebook.com/****************/post...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>143859782330858_****************</td>\n", "      <td>🏠💬 Myth Busted! Think home insurance breaks th...</td>\n", "      <td>2025-06-28T16:30:04+0000</td>\n", "      <td>https://www.facebook.com/****************/post...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>143859782330858_1065900278937670</td>\n", "      <td>🏠 Did you know? Between 2021 and 2024, home in...</td>\n", "      <td>2025-06-26T16:30:06+0000</td>\n", "      <td>https://www.facebook.com/****************/post...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 id  \\\n", "0  143859782330858_1070505058477192   \n", "1  143859782330858_1069041158623582   \n", "2  143859782330858_****************   \n", "3  143859782330858_****************   \n", "4  143859782330858_1065900278937670   \n", "\n", "                                             message  \\\n", "0  To our valued customers — we’re grateful for t...   \n", "1  It was a great venue to have the Peoria Chambe...   \n", "2  💬 Got questions about insurance? We’re all ear...   \n", "3  🏠💬 Myth Busted! Think home insurance breaks th...   \n", "4  🏠 Did you know? Between 2021 and 2024, home in...   \n", "\n", "               created_time                                      permalink_url  \n", "0  2025-07-02T16:30:09+0000  https://www.facebook.com/****************/post...  \n", "1  2025-06-30T20:42:00+0000  https://www.facebook.com/****************/post...  \n", "2  2025-06-30T16:30:04+0000  https://www.facebook.com/****************/post...  \n", "3  2025-06-28T16:30:04+0000  https://www.facebook.com/****************/post...  \n", "4  2025-06-26T16:30:06+0000  https://www.facebook.com/****************/post...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# fb_retrieval.py\n", "\n", "import requests\n", "import pandas as pd\n", "\n", "def get_facebook_posts(limit=10):\n", "    env = load_env()\n", "    url = f\"https://graph.facebook.com/v19.0/{env['FB_PAGE_ID']}/posts\"\n", "    params = {\n", "        \"access_token\": env[\"FB_PAGE_ACCESS_TOKEN\"],\n", "        \"limit\": limit,\n", "        \"fields\": \"id,message,created_time,permalink_url\"\n", "    }\n", "    response = requests.get(url, params=params)\n", "    data = response.json()\n", "\n", "    if \"data\" not in data:\n", "        raise Exception(\"Error fetching FB posts:\", data)\n", "\n", "    return pd.DataFrame(data[\"data\"])\n", "\n", "# Facebook Posts\n", "fb_posts = get_facebook_posts(limit=5)\n", "display(fb_posts)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "2fea1f5a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>caption</th>\n", "      <th>media_type</th>\n", "      <th>media_url</th>\n", "      <th>timestamp</th>\n", "      <th>permalink</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>17972498537871878</td>\n", "      <td>To our valued customers — we’re grateful for t...</td>\n", "      <td>IMAGE</td>\n", "      <td>https://scontent.cdninstagram.com/v/t51.82787-...</td>\n", "      <td>2025-07-03T05:57:17+0000</td>\n", "      <td>https://www.instagram.com/p/DLonxq8sBv4/</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18074194561756580</td>\n", "      <td>To our valued customers — we’re grateful for t...</td>\n", "      <td>IMAGE</td>\n", "      <td>https://scontent.cdninstagram.com/v/t39.30808-...</td>\n", "      <td>2025-07-02T16:30:12+0000</td>\n", "      <td>https://www.instagram.com/p/DLnLa4LpFFO/</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>17911289730118544</td>\n", "      <td>💬 Got questions about insurance? We’re all ear...</td>\n", "      <td>IMAGE</td>\n", "      <td>https://scontent.cdninstagram.com/v/t39.30808-...</td>\n", "      <td>2025-06-30T16:30:05+0000</td>\n", "      <td>https://www.instagram.com/p/DLiB0TKuCj7/</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>*****************</td>\n", "      <td>🏠💬 Myth Busted! Think home insurance breaks th...</td>\n", "      <td>IMAGE</td>\n", "      <td>https://scontent.cdninstagram.com/v/t39.30808-...</td>\n", "      <td>2025-06-28T16:30:09+0000</td>\n", "      <td>https://www.instagram.com/p/DLc4O_lqfhy/</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>*****************</td>\n", "      <td>🏠 Did you know? Between 2021 and 2024, home in...</td>\n", "      <td>IMAGE</td>\n", "      <td>https://scontent.cdninstagram.com/v/t39.30808-...</td>\n", "      <td>2025-06-26T16:30:10+0000</td>\n", "      <td>https://www.instagram.com/p/DLXupW4suab/</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  id                                            caption  \\\n", "0  17972498537871878  To our valued customers — we’re grateful for t...   \n", "1  18074194561756580  To our valued customers — we’re grateful for t...   \n", "2  17911289730118544  💬 Got questions about insurance? We’re all ear...   \n", "3  *****************  🏠💬 Myth Busted! Think home insurance breaks th...   \n", "4  *****************  🏠 Did you know? Between 2021 and 2024, home in...   \n", "\n", "  media_type                                          media_url  \\\n", "0      IMAGE  https://scontent.cdninstagram.com/v/t51.82787-...   \n", "1      IMAGE  https://scontent.cdninstagram.com/v/t39.30808-...   \n", "2      IMAGE  https://scontent.cdninstagram.com/v/t39.30808-...   \n", "3      IMAGE  https://scontent.cdninstagram.com/v/t39.30808-...   \n", "4      IMAGE  https://scontent.cdninstagram.com/v/t39.30808-...   \n", "\n", "                  timestamp                                 permalink  \n", "0  2025-07-03T05:57:17+0000  https://www.instagram.com/p/DLonxq8sBv4/  \n", "1  2025-07-02T16:30:12+0000  https://www.instagram.com/p/DLnLa4LpFFO/  \n", "2  2025-06-30T16:30:05+0000  https://www.instagram.com/p/DLiB0TKuCj7/  \n", "3  2025-06-28T16:30:09+0000  https://www.instagram.com/p/DLc4O_lqfhy/  \n", "4  2025-06-26T16:30:10+0000  https://www.instagram.com/p/DLXupW4suab/  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# ig_retrieval.py\n", "\n", "import requests\n", "import pandas as pd\n", "\n", "def get_instagram_posts(limit=10):\n", "    env = load_env()\n", "    url = f\"https://graph.facebook.com/v19.0/{env['IG_USER_ID']}/media\"\n", "    params = {\n", "        \"access_token\": env[\"ACCESS_TOKEN\"],\n", "        \"limit\": limit,\n", "        \"fields\": \"id,caption,media_type,media_url,timestamp,permalink\"\n", "    }\n", "    response = requests.get(url, params=params)\n", "    data = response.json()\n", "\n", "    if \"data\" not in data:\n", "        raise Exception(\"Error fetching IG posts:\", data)\n", "\n", "    return pd.DataFrame(data[\"data\"])\n", "\n", "# Instagram Posts\n", "ig_posts = get_instagram_posts(limit=5)\n", "display(ig_posts)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "a75cf019", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'media_id': '17972498537871878', 'caption': 'To our valued customers — we’re grateful for the trust you place in us to safeguard what matters most. Your continued support truly means the world to us! 💙🙌\\n\\n#GratefulHeart #ThankYou #CustomerAppreciation #WeValueYou\\n\\n📍Glendale, AZ | Call us at ************\\n📍Surprise, AZ | Call us at ************\\n📍Grand Junction | Call us at ************', 'media_type': 'IMAGE', 'media_url': 'https://scontent.cdninstagram.com/v/t51.82787-15/515234610_17952529748974561_4112220369512504316_n.jpg?stp=dst-jpg_e35_tt6&_nc_cat=109&ccb=1-7&_nc_sid=18de74&_nc_ohc=Rn0KdT62p1oQ7kNvwHy_0JQ&_nc_oc=AdkROyJL24_XafEdJmkEi3JVVx0K8euMqT_cuKWqh372oRY-k1NTEwz9IEQz-5Y6Qq4&_nc_zt=23&_nc_ht=scontent.cdninstagram.com&edm=AM6HXa8EAAAA&_nc_gid=hQJSxdl9VbgeBGBa4wFfVg&oh=00_AfOiReV2Xg4YLh5acfksrzAvPgMLEh83YaHy796FeMiuoQ&oe=686C0D9D', 'timestamp': '2025-07-03T05:57:17+0000', 'like_count': 0, 'comments_count': 0}\n", "{'media_id': '18074194561756580', 'caption': 'To our valued customers — we’re grateful for the trust you place in us to safeguard what matters most. Your continued support truly means the world to us! 💙🙌\\n\\n#GratefulHeart #ThankYou #CustomerAppreciation #WeValueYou\\n\\n📍Glendale, AZ | Call us at ************\\n📍Surprise, AZ | Call us at ************\\n📍Grand Junction | Call us at ************', 'media_type': 'IMAGE', 'media_url': 'https://scontent.cdninstagram.com/v/t39.30808-6/507394112_1056583793202652_3747817656684367276_n.jpg?stp=dst-jpg_e35_tt6&_nc_cat=102&ccb=1-7&_nc_sid=18de74&_nc_ohc=hUZ9OoIyf_MQ7kNvwHU4FY-&_nc_oc=AdlIfGwBJe6cBLJ0czijFywAELhNdd0vzEyk2Qwpksgakim2CyPG9gJqRndvKSXsWVY&_nc_zt=23&_nc_ht=scontent.cdninstagram.com&edm=AM6HXa8EAAAA&_nc_gid=hQJSxdl9VbgeBGBa4wFfVg&oh=00_AfN82QbBRBuCbrn0z9sNGczIbiNBfu-paJ0MGOEmeI1zOA&oe=686C0B63', 'timestamp': '2025-07-02T16:30:12+0000', 'like_count': 2, 'comments_count': 0}\n", "{'media_id': '17911289730118544', 'caption': '💬 Got questions about insurance? We’re all ears! Whether it’s auto, home, health, or life—we’re here to clear the confusion. Drop your questions below! ⬇️\\n\\n📲 DM us to know more today!\\n\\n#AskUsAnything #InsuranceExplained #HereToHelp #InsuranceMadeSimple\\n\\n📍 Glendale, AZ | Call us at ************\\n📍 Surprise, AZ | Call us at ************\\n📍Grand Junction | Call us at ************', 'media_type': 'IMAGE', 'media_url': 'https://scontent.cdninstagram.com/v/t39.30808-6/506975083_1056581103202921_7244211704844187948_n.jpg?stp=dst-jpg_e35_tt6&_nc_cat=111&ccb=1-7&_nc_sid=18de74&_nc_ohc=OwWzEkEzxD8Q7kNvwEVtnVw&_nc_oc=AdmOTqSHAaGXosocaRAdAoNpsiCVEq_gvouoNDEpRQLUposy0wrvj3IRNq2u8DUaj1M&_nc_zt=23&_nc_ht=scontent.cdninstagram.com&edm=AM6HXa8EAAAA&_nc_gid=hQJSxdl9VbgeBGBa4wFfVg&oh=00_AfOdCK0iqrze33WzOqwHhjzcENw-QikhLUyRqC5Mu2s9iA&oe=686BE01C', 'timestamp': '2025-06-30T16:30:05+0000', 'like_count': 0, 'comments_count': 0}\n", "{'media_id': '*****************', 'caption': '🏠💬 Myth Busted! Think home insurance breaks the bank?\\nThe truth: It’s more affordable than you think—and it protects what matters most. Peace of mind doesn’t have to come with a high price tag.\\n\\n📲 DM us to know more today!\\n\\n#MythVsFact #HomeInsurance #AffordableProtection #SmartCoverage #InsuranceMadeSimple\\n\\n📍 Glendale, AZ | Call us at ************\\n📍 Surprise, AZ | Call us at ************\\n📍Grand Junction | Call us at ************', 'media_type': 'IMAGE', 'media_url': 'https://scontent.cdninstagram.com/v/t39.30808-6/506987836_1056580406536324_8052168325459364975_n.jpg?stp=dst-jpg_e35_tt6&_nc_cat=106&ccb=1-7&_nc_sid=18de74&_nc_ohc=2n3LVKWNuboQ7kNvwFwXADe&_nc_oc=Adn3ftYOAY4fSmME_PQSaiKpANkZh3d_TBxpDXHeJdu0W7eGBvPRMub0AouQAVek54o&_nc_zt=23&_nc_ht=scontent.cdninstagram.com&edm=AM6HXa8EAAAA&_nc_gid=hQJSxdl9VbgeBGBa4wFfVg&oh=00_AfNQ5xUGbqjMJUNLmb5S48rsDr85os2aGevkcVL6307VfQ&oe=686BFC23', 'timestamp': '2025-06-28T16:30:09+0000', 'like_count': 0, 'comments_count': 0}\n", "{'media_id': '*****************', 'caption': '🏠 Did you know? Between 2021 and 2024, home insurance premiums rose by as much as 59% in states like Utah, Illinois, and Nebraska—fueled by inflation and extreme weather. Now’s the time to review your coverage and stay protected.\\n\\n📲 DM us to know more today!\\n\\n#DidYouKnow #HomeInsurance #RisingPremiums #BePrepared #InsuranceAwareness\\n\\n📍Glendale, AZ | Call us at ************\\n📍Surprise, AZ | Call us at ************\\n📍Grand Junction | Call us at ************', 'media_type': 'IMAGE', 'media_url': 'https://scontent.cdninstagram.com/v/t39.30808-6/506585687_1056579853203046_6620326597233356485_n.jpg?stp=dst-jpg_e35_tt6&_nc_cat=105&ccb=1-7&_nc_sid=18de74&_nc_ohc=zU9tSdlbFMkQ7kNvwEzOOc8&_nc_oc=Adnn6je8gdcBwVxl4oEsp2IFWZBPZluLIt8ph0T8mEb6WPN9EhGmzrk9TUY61gVXwb8&_nc_zt=23&_nc_ht=scontent.cdninstagram.com&edm=AM6HXa8EAAAA&_nc_gid=hQJSxdl9VbgeBGBa4wFfVg&oh=00_AfMaPMITbqwURpTEG5aTbUSoExwedEAppphY8EeC51VBjA&oe=686BEA9F', 'timestamp': '2025-06-26T16:30:10+0000', 'like_count': 0, 'comments_count': 0}\n"]}], "source": ["# fetching ig_analytics\n", "import requests\n", "env=load_env()  # adjust if load_env is defined elsewhere\n", "\n", "def get_instagram_post_insights(ig_user_id, access_token):\n", "    # Step 1: Get media with likes and comments\n", "    media_url = f\"https://graph.facebook.com/v19.0/{ig_user_id}/media\"\n", "    media_params = {\n", "        \"fields\": \"id,caption,media_type,media_url,timestamp,like_count,comments_count\",\n", "        \"access_token\": access_token,\n", "        \"limit\": 5\n", "    }\n", "    media_res = requests.get(media_url, params=media_params).json()\n", "\n", "    insights_data = []\n", "\n", "    for media in media_res.get(\"data\", []):\n", "        media_id = media[\"id\"]\n", "        \n", "        insights_url = f\"https://graph.facebook.com/v19.0/{media_id}/insights\"\n", "        metrics = \"impressions,reach,engagement,saved,shares,video_views\"\n", "        insights_params = {\n", "            \"metric\": metrics,\n", "            \"access_token\": access_token\n", "        }\n", "\n", "        insights_res = requests.get(insights_url, params=insights_params).json()\n", "\n", "        # Format results\n", "        data = {\n", "            \"media_id\": media_id,\n", "            \"caption\": media.get(\"caption\", \"\"),\n", "            \"media_type\": media.get(\"media_type\", \"\"),\n", "            \"media_url\": media.get(\"media_url\", \"\"),\n", "            \"timestamp\": media.get(\"timestamp\", \"\"),\n", "            \"like_count\": media.get(\"like_count\", 0),\n", "            \"comments_count\": media.get(\"comments_count\", 0)\n", "        }\n", "\n", "        for item in insights_res.get(\"data\", []):\n", "            data[item[\"name\"]] = item[\"values\"][0][\"value\"]\n", "        \n", "        insights_data.append(data)\n", "    \n", "    return insights_data\n", "\n", "# Load environment and run\n", "env = load_env()\n", "IG_USER_ID = env['IG_USER_ID']\n", "ig_insights = get_instagram_post_insights(IG_USER_ID, env[\"ACCESS_TOKEN\"])\n", "for i in ig_insights:\n", "    print(i)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "85fc3035", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Facebook Post Insights ---\n", "{'post_id': '143859782330858_1070505058477192', 'message': 'To our valued customers — we’re grateful for the trust you place in us to safeguard what matters most. Your continued support truly means the world to us! 💙🙌\\n\\n#GratefulHeart #ThankYou #CustomerAppreciation #WeValueYou\\n\\n📍Glendale, AZ | Call us at ************\\n📍Surprise, AZ  | Call us at ************\\n📍Grand Junction | Call us at ************', 'created_time': '2025-07-02T16:30:09+0000', 'permalink_url': 'https://www.facebook.com/****************/posts/1070505058477192', 'shares': 1, 'comments': 0, 'likes': 0, 'reach': 0, 'clicks': 0, 'engaged_users': 0}\n", "{'post_id': '143859782330858_1069041158623582', 'message': \"It was a great venue to have the Peoria Chamber of Commerce, Arizona Business After Hours Shenanigans! <PERSON> of Ideal Insurance Agency is a proud sponsor of this amazing monthly mixer!!! Cogratualations to <PERSON> for winning the Kneader's Gift Basket and to all the Super Networkers and Members - Congratulations and thanks for coming out to the event!!!\", 'created_time': '2025-06-30T20:42:00+0000', 'permalink_url': 'https://www.facebook.com/****************/posts/1069041158623582', 'shares': 0, 'comments': 0, 'likes': 0, 'reach': 0, 'clicks': 0, 'engaged_users': 0}\n", "{'post_id': '143859782330858_****************', 'message': '💬 Got questions about insurance? We’re all ears! Whether it’s auto, home, health, or life—we’re here to clear the confusion. Drop your questions below! ⬇️\\n\\n📲 DM us to know more today!\\n\\n#AskUsAnything #InsuranceExplained #HereToHelp #InsuranceMadeSimple\\n\\n📍 Glendale, AZ | Call us at ************\\n📍 Surprise, AZ  | Call us at ************\\n📍Grand Junction | Call us at ************', 'created_time': '2025-06-30T16:30:04+0000', 'permalink_url': 'https://www.facebook.com/****************/posts/****************', 'shares': 1, 'comments': 0, 'likes': 0, 'reach': 0, 'clicks': 0, 'engaged_users': 0}\n", "{'post_id': '143859782330858_****************', 'message': '🏠💬 Myth Busted! Think home insurance breaks the bank?\\nThe truth: It’s more affordable than you think—and it protects what matters most. Peace of mind doesn’t have to come with a high price tag.\\n\\n📲 DM us to know more today!\\n\\n#MythVsFact #HomeInsurance #AffordableProtection #SmartCoverage #InsuranceMadeSimple\\n\\n📍 Glendale, AZ | Call us at ************\\n📍 Surprise, AZ  | Call us at ************\\n📍Grand Junction | Call us at ************', 'created_time': '2025-06-28T16:30:04+0000', 'permalink_url': 'https://www.facebook.com/****************/posts/****************', 'shares': 1, 'comments': 0, 'likes': 0, 'reach': 0, 'clicks': 0, 'engaged_users': 0}\n", "{'post_id': '143859782330858_1065900278937670', 'message': '🏠 Did you know? Between 2021 and 2024, home insurance premiums rose by as much as 59% in states like Utah, Illinois, and Nebraska—fueled by inflation and extreme weather. Now’s the time to review your coverage and stay protected.\\n\\n📲 DM us to know more today!\\n\\n#DidYouKnow #HomeInsurance #RisingPremiums #BePrepared #InsuranceAwareness\\n\\n📍Glendale, AZ | Call us at ************\\n📍Surprise, AZ  | Call us at ************\\n📍Grand Junction | Call us at ************', 'created_time': '2025-06-26T16:30:06+0000', 'permalink_url': 'https://www.facebook.com/****************/posts/1065900278937670', 'shares': 0, 'comments': 0, 'likes': 0, 'reach': 0, 'clicks': 0, 'engaged_users': 0}\n"]}], "source": ["# fetching fb analytics\n", "import requests\n", "env= load_env()\n", "\n", "def get_facebook_post_insights(page_id, page_access_token):\n", "    posts_url = f\"https://graph.facebook.com/v19.0/{page_id}/posts\"\n", "    post_params = {\n", "        \"fields\": \"id,message,created_time,permalink_url,shares\",\n", "        \"access_token\": page_access_token,\n", "        \"limit\": 5\n", "    }\n", "    posts_res = requests.get(posts_url, params=post_params).json()\n", "\n", "    insights_data = []\n", "\n", "    for post in posts_res.get(\"data\", []):\n", "        post_id = post[\"id\"]\n", "        insights_url = f\"https://graph.facebook.com/v19.0/{post_id}/insights\"\n", "        metrics = \"post_impressions_unique,post_clicks,post_engaged_users,post_reactions_by_type_total\"\n", "        insights_params = {\n", "            \"metric\": metrics,\n", "            \"access_token\": page_access_token\n", "        }\n", "        insights_res = requests.get(insights_url, params=insights_params).json()\n", "\n", "        # Get comments count via summary\n", "        comments_url = f\"https://graph.facebook.com/v19.0/{post_id}/comments\"\n", "        comments_params = {\n", "            \"summary\": \"true\",\n", "            \"access_token\": page_access_token\n", "        }\n", "        comments_res = requests.get(comments_url, params=comments_params).json()\n", "        comments_count = comments_res.get(\"summary\", {}).get(\"total_count\", 0)\n", "\n", "        data = {\n", "            \"post_id\": post_id,\n", "            \"message\": post.get(\"message\", \"\"),\n", "            \"created_time\": post.get(\"created_time\", \"\"),\n", "            \"permalink_url\": post.get(\"permalink_url\", \"\"),\n", "            \"shares\": post.get(\"shares\", {}).get(\"count\", 0),\n", "            \"comments\": comments_count,\n", "            \"likes\": 0,  # Will extract from reactions below\n", "            \"reach\": 0,\n", "            \"clicks\": 0,\n", "            \"engaged_users\": 0\n", "        }\n", "\n", "        for item in insights_res.get(\"data\", []):\n", "            if item[\"name\"] == \"post_reactions_by_type_total\":\n", "                reactions = item[\"values\"][0][\"value\"]\n", "                data[\"likes\"] = reactions.get(\"like\", 0)\n", "            elif item[\"name\"] == \"post_impressions_unique\":\n", "                data[\"reach\"] = item[\"values\"][0][\"value\"]\n", "            elif item[\"name\"] == \"post_clicks\":\n", "                data[\"clicks\"] = item[\"values\"][0][\"value\"]\n", "            elif item[\"name\"] == \"post_engaged_users\":\n", "                data[\"engaged_users\"] = item[\"values\"][0][\"value\"]\n", "\n", "        insights_data.append(data)\n", "\n", "    return insights_data\n", "\n", "\n", "# --- Run ---\n", "env = load_env()\n", "print(\"\\n--- Facebook Post Insights ---\")\n", "fb_insights = get_facebook_post_insights(env[\"FB_PAGE_ID\"], env[\"FB_PAGE_ACCESS_TOKEN\"])\n", "for f in fb_insights:\n", "    print(f)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c66b46b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 📝 Preview of Reposted Content ---\n", "📝 Message: This 4th of July, we honor the strength of our nation and the freedom we cherish.\n", "At Ideal Insurance, we’re proud to protect what matters most—your family, your business, your future.\n", "Wishing you a safe and joyful Independence Day!\n", "\n", "#IndependenceDay #4thOfJuly #IdealInsuranceAZ #ProtectWhatMatters #ProudToServe\n", "\n", "📍Glendale, AZ | Call us at ************\n", "📍Surprise, AZ  | Call us at ************\n", "📍Grand Junction | Call us at ************\n", "❌ Failed to repost: {'error': {'message': 'Application does not have permission for this action', 'type': 'OAuthException', 'code': 10, 'error_subcode': 2069007, 'is_transient': False, 'error_user_title': 'Permissions Deny to generate story', 'error_user_msg': 'The story cannot be generated because the permissions deny.', 'fbtrace_id': 'AomVvS-csdYB6dZSUXCzmdU'}}\n"]}], "source": ["import requests\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "# Load env variables\n", "def load_env():\n", "    load_dotenv()\n", "    return {\n", "        \"ACCESS_TOKEN\": os.getenv(\"ACCESS_TOKEN\"),\n", "        \"FB_PAGE_ID\": os.getenv(\"FB_PAGE_ID\"),\n", "        \"FB_PAGE_ACCESS_TOKEN\": os.getenv(\"FB_PAGE_ACCESS_TOKEN\")\n", "    }\n", "\n", "def repost_and_delete_latest_fb_post():\n", "    env = load_env()\n", "    access_token = env[\"ACCESS_TOKEN\"]\n", "    page_id = env[\"FB_PAGE_ID\"]\n", "    page_access_token = env[\"FB_PAGE_ACCESS_TOKEN\"]\n", "\n", "    # Step 1: Get latest post\n", "    post_url = f\"https://graph.facebook.com/v19.0/{page_id}/posts\"\n", "    params = {\n", "        \"access_token\": page_access_token,\n", "        \"limit\": 1,\n", "        \"fields\": \"id,message\"\n", "    }\n", "    res = requests.get(post_url, params=params).json()\n", "    posts = res.get(\"data\", [])\n", "    if not posts:\n", "        print(\"❌ No posts found. Full response:\")\n", "        print(res)\n", "\n", "        return\n", "\n", "    latest_post = posts[0]\n", "    post_id = latest_post[\"id\"]\n", "    message = latest_post.get(\"message\", \"\")\n", "\n", "    # Step 2: Check if it has image attachment\n", "    details_url = f\"https://graph.facebook.com/v19.0/{post_id}\"\n", "    details_params = {\n", "        \"fields\": \"message,attachments{media_type,media,target}\",\n", "        \"access_token\": access_token\n", "    }\n", "    details_res = requests.get(details_url, params=details_params).json()\n", "\n", "    attachments = details_res.get(\"attachments\", {}).get(\"data\", [])\n", "    media_fbid = None\n", "    for att in attachments:\n", "        if att.get(\"media_type\") == \"photo\":\n", "            media_fbid = att.get(\"media\", {}).get(\"target\", {}).get(\"id\")\n", "\n", "    # Step 3: Preview\n", "    print(\"\\n--- 📝 Preview of Reposted Content ---\")\n", "    print(\"📝 Message:\", message)\n", "    if media_fbid:\n", "        print(\"📷 Image media_fbid:\", media_fbid)\n", "\n", "    confirm_post = input(\"\\nDo you want to repost this? [Y/N]: \").strip().lower()\n", "    if confirm_post != \"y\":\n", "        print(\"❎ Repost canceled.\")\n", "        return\n", "\n", "    # Step 4: Repost\n", "    post_url = f\"https://graph.facebook.com/v19.0/{page_id}/feed\"\n", "    if media_fbid:\n", "        post_data = {\n", "            \"message\": message,\n", "            \"attached_media\": [{\"media_fbid\": media_fbid}],\n", "            \"access_token\": page_access_token\n", "        }\n", "        response = requests.post(post_url, json=post_data).json()\n", "    else:\n", "        post_data = {\n", "            \"message\": message,\n", "            \"access_token\": page_access_token\n", "        }\n", "        response = requests.post(post_url, data=post_data).json()\n", "\n", "    if \"id\" not in response:\n", "        print(\"❌ Failed to repost:\", response)\n", "        return\n", "\n", "    new_post_id = response[\"id\"]\n", "    print(\"✅ Reposted successfully! New Post ID:\", new_post_id)\n", "\n", "    # Step 5: Ask to delete the reposted post\n", "    confirm_delete = input(f\"\\n⚠️ Do you want to delete the reposted post ({new_post_id}) now? [Y/N]: \").strip().lower()\n", "    if confirm_delete == \"y\":\n", "        delete_url = f\"https://graph.facebook.com/v19.0/{new_post_id}\"\n", "        delete_params = {\"access_token\": page_access_token}\n", "        delete_res = requests.delete(delete_url, params=delete_params)\n", "\n", "        if delete_res.status_code == 200:\n", "            print(f\"🗑️ Post {new_post_id} deleted successfully.\")\n", "        else:\n", "            print(\"❌ Failed to delete post:\", delete_res.json())\n", "    else:\n", "        print(\"🛑 Deletion skipped. Post is live!\")\n", "\n", "# --- Run ---\n", "repost_and_delete_latest_fb_post()\n"]}, {"cell_type": "code", "execution_count": null, "id": "0e6bac7f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}