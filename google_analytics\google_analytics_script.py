import matplotlib.pyplot as plt
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.lib.units import mm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Image, Spacer
from reportlab.lib.styles import getSampleStyleSheet
import io

def create_line_chart():
    # Sample data
    days = list(range(1, 32))
    sessions = [300 + (i % 5) * 20 + (i % 7) * 15 for i in days]
    engaged_sessions = [s - 20 for s in sessions]
    plt.figure(figsize=(5,2))
    plt.plot(days, sessions, label='Sessions')
    plt.plot(days, engaged_sessions, label='Engaged sessions')
    plt.legend()
    plt.title('Sessions vs Engaged Sessions')
    plt.xlabel('March')
    plt.tight_layout()
    buf = io.BytesIO()
    plt.savefig(buf, format='PNG')
    plt.close()
    buf.seek(0)
    return buf

def create_pie_chart():
    # Sample data
    labels = ['Organic Search', 'Paid Other', 'Direct', 'Unassigned', 'Referral', 'Organic Social']
    sizes = [72.1, 11.8, 9.3, 3, 2, 1.8]
    plt.figure(figsize=(2,2))
    plt.pie(sizes, labels=labels, autopct='%1.1f%%')
    plt.title('Sessions by channel')
    buf = io.BytesIO()
    plt.savefig(buf, format='PNG')
    plt.close()
    buf.seek(0)
    return buf

def seo_report(output_file):
    doc = SimpleDocTemplate(output_file, pagesize=landscape(A4), rightMargin=10, leftMargin=10, topMargin=10, bottomMargin=10)
    styles = getSampleStyleSheet()
    elements = []

    # Header
    elements.append(Paragraph('<b><font size=18 color="#0073e6">LocaliQ</font></b>', styles['Title']))
    elements.append(Paragraph('<b>SEO Snapshot: Monthly Review</b>', styles['Title']))
    elements.append(Paragraph('How is my campaign performing?', styles['Normal']))
    elements.append(Spacer(1, 8))

    # Info Box
    info = '<b>For questions regarding this data or your SEO program in general, please contact your Digital Presence Manager (DPM).</b>'
    elements.append(Paragraph(info, styles['Normal']))
    elements.append(Spacer(1, 6))

    # Metrics Table
    metrics_data = [
        ['Impressions', 'Clicks', 'Total users', 'New users', 'Sessions', 'Engaged sessions'],
        ['1,396', '34', '9,275', '9,126', '10,274', '10,028'],
        ['Engagement rate', 'Avg engagement time', 'Event count', 'Events per session', 'Pageviews', 'Views per session'],
        ['97.61%', '00:00:46', '42,853', '4.17', '12,747', '1.24']
    ]
    metrics_table = Table(metrics_data, colWidths=60)
    metrics_table.setStyle(TableStyle([
        ('BACKGROUND', (0,0), (-1,0), colors.lightblue),
        ('BACKGROUND', (0,2), (-1,2), colors.lightblue),
        ('TEXTCOLOR', (0,0), (-1,-1), colors.black),
        ('FONTNAME', (0,0), (-1,-1), 'Helvetica-Bold'),
        ('ALIGN', (0,0), (-1,-1), 'CENTER'),
        ('GRID', (0,0), (-1,-1), 0.5, colors.grey)
    ]))
    elements.append(metrics_table)
    elements.append(Spacer(1, 12))

    # Line Chart
    line_chart = create_line_chart()
    elements.append(Image(line_chart, width=300, height=120))
    elements.append(Spacer(1, 12))

    # Tables (Top search queries example)
    search_queries_data = [
        ['Top search queries', 'Impressions'],
        ['arizona vein and laser institute', 72],
        ['arizona vein and vascular center', 52],
        ['arizona vein and vascular', 38],
        ['vein specialist scottsdale', 36],
        # ... add more rows as needed
    ]
    search_queries_table = Table(search_queries_data, colWidths=[180, 70])
    search_queries_table.setStyle(TableStyle([
        ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
        ('ALIGN', (1,1), (-1,-1), 'CENTER'),
        ('GRID', (0,0), (-1,-1), 0.5, colors.grey)
    ]))
    elements.append(search_queries_table)
    elements.append(Spacer(1, 12))

    # Pie Chart
    pie_chart = create_pie_chart()
    elements.append(Image(pie_chart, width=120, height=120))
    elements.append(Spacer(1, 12))

    # Sessions by device table
    device_data = [
        ['Sessions by device', ''],
        ['mobile', '8,381'],
        ['desktop', '1,830'],
        ['tablet', '177'],
    ]
    device_table = Table(device_data, colWidths=[120, 60])
    device_table.setStyle(TableStyle([
        ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
        ('ALIGN', (1,1), (-1,-1), 'CENTER'),
        ('GRID', (0,0), (-1,-1), 0.5, colors.grey)
    ]))
    elements.append(device_table)

    doc.build(elements)

# Usage
seo_report("seo_report.pdf")