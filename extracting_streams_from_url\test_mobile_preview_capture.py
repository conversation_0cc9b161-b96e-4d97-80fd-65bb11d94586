"""
Test script for mobile preview capture improvements

This script demonstrates the enhanced capture methods specifically designed
to address the issue where element screenshots capture blank areas instead
of the actual mobile preview content.

Key improvements tested:
1. Multiple capture method fallbacks
2. Content analysis to detect blank captures
3. Iframe-specific targeting
4. Enhanced debugging and analysis
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from PIL import Image
from io import BytesIO

def setup_driver():
    """Setup Chrome driver with optimal settings"""
    options = webdriver.ChromeOptions()
    options.add_argument("--window-size=1920,1080")
    options.add_argument("--disable-web-security")
    options.add_argument("--disable-features=VizDisplayCompositor")
    
    driver = webdriver.Chrome(options=options)
    driver.maximize_window()
    return driver

def analyze_preview_structure(driver):
    """Analyze the structure of the mobile preview"""
    try:
        wait = WebDriverWait(driver, 30)
        
        # Find preview elements
        preview_card = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".preview-card")))
        resize_ph = preview_card.find_element(By.CSS_SELECTOR, ".resize-ph")
        iframe = resize_ph.find_element(By.CSS_SELECTOR, "iframe")
        
        print("=== Preview Structure Analysis ===")
        print(f"Preview card location: {preview_card.location}")
        print(f"Preview card size: {preview_card.size}")
        print(f"Resize-ph location: {resize_ph.location}")
        print(f"Resize-ph size: {resize_ph.size}")
        print(f"Iframe location: {iframe.location}")
        print(f"Iframe size: {iframe.size}")
        
        # Check iframe content
        driver.switch_to.frame(iframe)
        body_height = driver.execute_script("return document.body.scrollHeight")
        client_height = driver.execute_script("return document.documentElement.clientHeight")
        print(f"Iframe content height: {body_height}")
        print(f"Iframe client height: {client_height}")
        print(f"Is scrollable: {body_height > client_height}")
        driver.switch_to.default_content()
        
        return {
            'preview_card': preview_card,
            'resize_ph': resize_ph,
            'iframe': iframe,
            'is_scrollable': body_height > client_height
        }
        
    except Exception as e:
        print(f"Structure analysis failed: {e}")
        return None

def test_capture_methods(driver, elements):
    """Test different capture methods"""
    results = {}
    
    # Method 1: Resize-ph element screenshot
    print("\n=== Testing resize-ph element screenshot ===")
    try:
        screenshot_bytes = elements['resize_ph'].screenshot_as_png
        image = Image.open(BytesIO(screenshot_bytes))
        image.save("test_resize_ph_element.png")
        results['resize_ph_element'] = {
            'success': True,
            'size': image.size,
            'file': 'test_resize_ph_element.png'
        }
        print(f"✓ Resize-ph element capture: {image.size}")
    except Exception as e:
        print(f"✗ Resize-ph element capture failed: {e}")
        results['resize_ph_element'] = {'success': False, 'error': str(e)}
    
    # Method 2: Iframe element screenshot
    print("\n=== Testing iframe element screenshot ===")
    try:
        screenshot_bytes = elements['iframe'].screenshot_as_png
        image = Image.open(BytesIO(screenshot_bytes))
        image.save("test_iframe_element.png")
        results['iframe_element'] = {
            'success': True,
            'size': image.size,
            'file': 'test_iframe_element.png'
        }
        print(f"✓ Iframe element capture: {image.size}")
    except Exception as e:
        print(f"✗ Iframe element capture failed: {e}")
        results['iframe_element'] = {'success': False, 'error': str(e)}
    
    # Method 3: Full page with crop
    print("\n=== Testing full page with crop ===")
    try:
        # Get element position
        element_location = elements['resize_ph'].location
        element_size = elements['resize_ph'].size
        
        # Take full page screenshot
        screenshot_bytes = driver.get_screenshot_as_png()
        full_image = Image.open(BytesIO(screenshot_bytes))
        
        # Crop to element area
        left = element_location['x']
        top = element_location['y']
        right = left + element_size['width']
        bottom = top + element_size['height']
        
        cropped_image = full_image.crop((left, top, right, bottom))
        cropped_image.save("test_full_page_crop.png")
        
        results['full_page_crop'] = {
            'success': True,
            'size': cropped_image.size,
            'file': 'test_full_page_crop.png'
        }
        print(f"✓ Full page crop: {cropped_image.size}")
    except Exception as e:
        print(f"✗ Full page crop failed: {e}")
        results['full_page_crop'] = {'success': False, 'error': str(e)}
    
    return results

def analyze_image_content(image_path):
    """Analyze if an image has meaningful content"""
    try:
        image = Image.open(image_path)
        gray = image.convert('L')
        
        # Simple content analysis
        width, height = gray.size
        sample_pixels = []
        for i in range(0, width, max(1, width//20)):
            for j in range(0, height, max(1, height//20)):
                sample_pixels.append(gray.getpixel((i, j)))
        
        if len(sample_pixels) > 1:
            pixel_range = max(sample_pixels) - min(sample_pixels)
            avg_pixel = sum(sample_pixels) / len(sample_pixels)
            
            print(f"  Image: {image_path}")
            print(f"  Size: {image.size}")
            print(f"  Pixel range: {pixel_range}")
            print(f"  Average pixel value: {avg_pixel:.1f}")
            print(f"  Has content: {'Yes' if pixel_range > 30 else 'No (likely blank)'}")
            
            return pixel_range > 30
        
        return False
        
    except Exception as e:
        print(f"Content analysis failed for {image_path}: {e}")
        return False

def main():
    """Main test function"""
    print("Mobile Preview Capture Test")
    print("=" * 50)
    
    # Get URL from user
    url = input("Enter the URL to test (or press Enter for default): ").strip()
    if not url:
        url = "https://test.teamstreamz.com/"
    
    driver = setup_driver()
    
    try:
        print(f"Opening URL: {url}")
        driver.get(url)
        
        input("Navigate to a card preview and press Enter to continue...")
        
        # Analyze structure
        elements = analyze_preview_structure(driver)
        if not elements:
            print("Could not find preview elements")
            return
        
        # Test capture methods
        results = test_capture_methods(driver, elements)
        
        # Analyze captured images
        print("\n=== Content Analysis ===")
        for method, result in results.items():
            if result.get('success'):
                analyze_image_content(result['file'])
                print()
        
        print("\n=== Summary ===")
        print("Check the generated image files:")
        for method, result in results.items():
            if result.get('success'):
                print(f"  - {result['file']} ({method})")
        
        print("\nCompare these images with what you see in the browser preview.")
        print("The method that produces the most accurate result should be used.")
        
    except Exception as e:
        print(f"Test failed: {e}")
    
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    main()
