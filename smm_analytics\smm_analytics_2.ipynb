{"cells": [{"cell_type": "markdown", "id": "46ae87bd", "metadata": {}, "source": ["# Version 2\n", "\n", "instagram content publishing"]}, {"cell_type": "code", "execution_count": null, "id": "409ca262", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 📝 Preview of Reposted Content ---\n", "📸 Media URL: https://scontent.cdninstagram.com/v/t39.30808-6/507394112_1056583793202652_3747817656684367276_n.jpg?stp=dst-jpg_e35_tt6&_nc_cat=102&ccb=1-7&_nc_sid=18de74&_nc_ohc=hUZ9OoIyf_MQ7kNvwH15fBW&_nc_oc=Adk05zm5NLYVnv_GAlEX-QUi57P8Ck6bumu46uMIXpAgUa7BIRWa6snxY37L-sEsZ8ZHfb5GX0-x-lW64_z21lfr&_nc_zt=23&_nc_ht=scontent.cdninstagram.com&edm=AM6HXa8EAAAA&_nc_gid=WYH1gQb4QcYYtsPPMDsS8w&oh=00_AfP82lt4L-zVU_c6vuhig7rf1g3uWBZSxpXL6YFNcuUUAA&oe=686BD323\n", "📝 Caption: To our valued customers — we’re grateful for the trust you place in us to safeguard what matters most. Your continued support truly means the world to us! 💙🙌\n", "\n", "#GratefulHeart #ThankYou #CustomerAppreciation #WeValueYou\n", "\n", "📍Glendale, AZ | Call us at ************\n", "📍Surprise, AZ | Call us at 623-933-8263\n", "📍Grand Junction | Call us at 970-852-2981\n", "✅ Media container created: 18010867439750227\n", "✅ Reposted successfully! New Post ID: 17972498537871878\n", "❌ Failed to delete post: {'error': {'message': \"Unsupported delete request. Object with ID '17972498537871878' does not exist, cannot be loaded due to missing permissions, or does not support this operation. Please read the Graph API documentation at https://developers.facebook.com/docs/graph-api\", 'type': 'GraphMethodException', 'code': 100, 'error_subcode': 33, 'fbtrace_id': 'ANCJjdODtUJ3tydrdyhVrj6'}}\n"]}], "source": ["import requests\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "# Load env variables\n", "def load_env():\n", "    load_dotenv()\n", "    return {\n", "        \"ACCESS_TOKEN\": os.getenv(\"ACCESS_TOKEN\"),\n", "        \"IG_USER_ID\": os.getenv(\"IG_USER_ID\")\n", "    }\n", "\n", "# Main function\n", "def repost_and_delete_latest_ig_post():\n", "    env = load_env()\n", "    access_token = env[\"ACCESS_TOKEN\"]\n", "    ig_user_id = env[\"IG_USER_ID\"]\n", "\n", "    # Step 1: Get most recent post\n", "    recent_url = f\"https://graph.facebook.com/v19.0/{ig_user_id}/media\"\n", "    recent_params = {\n", "        \"fields\": \"id,media_type,media_url,caption,timestamp,permalink\",\n", "        \"access_token\": access_token,\n", "        \"limit\": 1\n", "    }\n", "\n", "    recent_res = requests.get(recent_url, params=recent_params).json()\n", "    if \"data\" not in recent_res or len(recent_res[\"data\"]) == 0:\n", "        print(\"❌ No recent post found.\")\n", "        return\n", "\n", "    recent_post = recent_res[\"data\"][0]\n", "    caption = recent_post.get(\"caption\", \"\")\n", "    media_url = recent_post.get(\"media_url\")\n", "    original_post_id = recent_post.get(\"id\")\n", "\n", "    print(\"\\n--- 📝 Preview of Reposted Content ---\")\n", "    print(\"📸 Media URL:\", media_url)\n", "    print(\"📝 Caption:\", caption)\n", "\n", "    confirm_post = input(\"\\nDo you want to repost this image? [Y/N]: \").strip().lower()\n", "    if confirm_post != \"y\":\n", "        print(\"❎ Post canceled.\")\n", "        return\n", "\n", "    # Step 2: Create media container\n", "    creation_url = f\"https://graph.facebook.com/v19.0/{ig_user_id}/media\"\n", "    creation_params = {\n", "        \"image_url\": media_url,\n", "        \"caption\": caption,\n", "        \"access_token\": access_token\n", "    }\n", "    creation_res = requests.post(creation_url, data=creation_params).json()\n", "\n", "    if \"id\" not in creation_res:\n", "        print(\"❌ Failed to create media container:\", creation_res)\n", "        return\n", "\n", "    creation_id = creation_res[\"id\"]\n", "    print(\"✅ Media container created:\", creation_id)\n", "\n", "    # Step 3: Publish media\n", "    publish_url = f\"https://graph.facebook.com/v19.0/{ig_user_id}/media_publish\"\n", "    publish_params = {\n", "        \"creation_id\": creation_id,\n", "        \"access_token\": access_token\n", "    }\n", "    publish_res = requests.post(publish_url, data=publish_params).json()\n", "\n", "    if \"id\" not in publish_res:\n", "        print(\"❌ Failed to publish post:\", publish_res)\n", "        return\n", "\n", "    new_post_id = publish_res[\"id\"]\n", "    print(\"✅ Reposted successfully! New Post ID:\", new_post_id)\n", "\n", "    # Step 4: Prompt and delete the new post\n", "    confirm_delete = input(f\"\\n⚠️ Do you want to delete the reposted post ({new_post_id}) now? [Y/N]: \").strip().lower()\n", "    if confirm_delete == \"y\":\n", "        delete_url = f\"https://graph.facebook.com/v19.0/{new_post_id}\"\n", "        delete_params = {\"access_token\": access_token}\n", "        delete_res = requests.delete(delete_url, params=delete_params)\n", "\n", "        if delete_res.status_code == 200:\n", "            print(f\"🗑️ Post {new_post_id} deleted successfully.\")\n", "        else:\n", "            print(\"❌ Failed to delete post:\", delete_res.json())\n", "    else:\n", "        print(\"🛑 Deletion skipped. Be careful, post is live!\")\n", "\n", "# --- Run ---\n", "repost_and_delete_latest_ig_post()\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}