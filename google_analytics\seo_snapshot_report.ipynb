{"cells": [{"cell_type": "markdown", "id": "e65f6fce", "metadata": {}, "source": ["# 📊 SEO Snapshot: Monthly Review Report\n", "This notebook fetches data from Google Analytics 4 and Google Search Console, then generates a PDF report exactly like the LocaliQ SEO Snapshot Monthly Review."]}, {"cell_type": "markdown", "id": "a36b978b", "metadata": {}, "source": ["## Step 1: Setup & Authentication\n", "Authenticate Google Analytics Data API and Search Console API. Be sure you have a valid `credentials.json` file from Google Cloud Console."]}, {"cell_type": "code", "execution_count": 1, "id": "4637a465", "metadata": {}, "outputs": [], "source": ["from google.oauth2 import service_account\n", "from google.analytics.data_v1beta import BetaAnalyticsDataClient\n", "from google.analytics.data_v1beta.types import RunReportRequest, DateRange, Metric, Dimension\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "from reportlab.lib.pagesizes import landscape, letter\n", "from reportlab.pdfgen import canvas\n", "from reportlab.lib.units import inch\n", "from reportlab.lib.colors import HexColor\n", "import dotenv\n", "import os\n", "\n", "dotenv.load_dotenv()\n", "# Set up credentials\n", "KEY_PATH = \"fabled-tesla-453318-h3-d12274a3efcf.json\"  # 👈 Replace with your JSON key path\n", "PROPERTY_ID = os.getenv(\"PROPERTY_ID\")                     # 👈 Replace with your GA4 property ID\n", "start_date = \"2024-03-01\"\n", "end_date = \"2024-03-31\"\n", "\n", "\n", "\n", "credentials = service_account.Credentials.from_service_account_file(KEY_PATH)\n", "ga_client = BetaAnalyticsDataClient(credentials=credentials)\n"]}, {"cell_type": "markdown", "id": "f4349d52", "metadata": {}, "source": ["## Step 2: Fetch Data from GA4 and Search Console"]}, {"cell_type": "code", "execution_count": 2, "id": "e7cc4af3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'totalUsers': 5188.0, 'newUsers': 5141.0, 'sessions': 5659.0, 'engagedSessions': 391.0, 'engagementRate': 0.****************, 'averageSessionDuration': 21.**************, 'eventCount': 17448.0, 'screenPageViews': 5849.0, 'userEngagementDuration': 14390.0, 'sessionsPerUser': 1.****************}\n"]}], "source": ["# 1. <PERSON><PERSON><PERSON> Metric<PERSON> (GA4)\n", "from google.analytics.data_v1beta.types import RunReportRequest, DateRange, Metric\n", "\n", "summary_request = RunReportRequest(\n", "    property=f\"properties/{PROPERTY_ID}\",\n", "    metrics=[\n", "        Met<PERSON>(name=\"totalUsers\"),\n", "        # Metric(name=\"clicks\"),\n", "        Metric(name=\"newUsers\"),\n", "        <PERSON><PERSON>(name=\"sessions\"),\n", "        <PERSON><PERSON>(name=\"engagedSessions\"),\n", "        <PERSON><PERSON>(name=\"engagementRate\"),\n", "        Metric(name=\"averageSessionDuration\"),\n", "        <PERSON><PERSON>(name=\"eventCount\"),\n", "        Metric(name=\"screenPageViews\"),\n", "        Metric(name=\"userEngagementDuration\"),\n", "        Met<PERSON>(name=\"sessionsPerUser\"),\n", "    ],\n", "    date_ranges=[DateRange(start_date=f\"{start_date}\", end_date=f'{end_date}')]\n", ")\n", "\n", "summary_response = ga_client.run_report(request=summary_request)\n", "summary_data = {row.dimension_values[0].value if row.dimension_values else m.name: float(row.metric_values[i].value)\n", "                for i, m in enumerate(summary_response.metric_headers)\n", "                for row in summary_response.rows}\n", "print(summary_data)"]}, {"cell_type": "code", "execution_count": 3, "id": "a63ce7c1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          City  Views\n", "0      Phoenix   1693\n", "1        Tempe    577\n", "2    (not set)    380\n", "3     Glendale     81\n", "4         Mesa     79\n", "5  Los Angeles     76\n", "6       Tucson     74\n", "7    Las Vegas     69\n", "8     Chandler     45\n", "9       Peoria     38\n"]}], "source": ["# 2. Top Cities by Traffic\n", "city_request = RunReportRequest(\n", "    property=f\"properties/{PROPERTY_ID}\",\n", "    dimensions=[{\"name\": \"city\"}],\n", "    metrics=[{\"name\": \"sessions\"}],\n", "    date_ranges=[{\"start_date\":f'{start_date}', \"end_date\": f'{end_date}'}],\n", "    order_bys=[{\"metric\": {\"metric_name\": \"sessions\"}, \"desc\": True}],\n", "    limit=10\n", ")\n", "\n", "city_response = ga_client.run_report(request=city_request)\n", "\n", "top_cities_df = pd.DataFrame([\n", "    {\"City\": row.dimension_values[0].value, \"Views\": int(row.metric_values[0].value)}\n", "    for row in city_response.rows\n", "])\n", "print(top_cities_df)"]}, {"cell_type": "code", "execution_count": 4, "id": "4bd7b094", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                    Page  Views\n", "0                  /home   5792\n", "1      /meet-the-founder     35\n", "2                      /      7\n", "3  /terms-and-conditions      6\n", "4         /app-downloads      5\n", "5          /choose-ycuuk      2\n", "6        /privacy-policy      2\n"]}], "source": ["# Top landing page\n", "page_request = RunReportRequest(\n", "    property=f\"properties/{PROPERTY_ID}\",\n", "    dimensions=[{\"name\": \"pagePath\"}],\n", "    metrics=[{\"name\": \"screenPageViews\"}],\n", "    date_ranges=[{\"start_date\": f'{start_date}', \"end_date\": f'{end_date}'}],\n", "    order_bys=[{\"metric\": {\"metric_name\": \"screenPageViews\"}, \"desc\": True}],\n", "    limit=10\n", ")\n", "\n", "page_response = ga_client.run_report(request=page_request)\n", "\n", "landing_pages_df = pd.DataFrame([\n", "    {\"Page\": row.dimension_values[0].value, \"Views\": int(row.metric_values[0].value)}\n", "    for row in page_response.rows\n", "])\n", "print(landing_pages_df)"]}, {"cell_type": "code", "execution_count": 5, "id": "4782ac19", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["         Date  Sessions  Engaged Sessions\n", "0  2024-03-02       435                18\n", "1  2024-03-23       345                54\n", "2  2024-03-06       322                18\n", "3  2024-03-25       321                12\n", "4  2024-03-04       320                28\n", "5  2024-03-01       293                12\n", "6  2024-03-05       251                 8\n", "7  2024-03-24       248                20\n", "8  2024-03-08       234                27\n", "9  2024-03-03       223                20\n", "10 2024-03-14       217                21\n", "11 2024-03-22       212                22\n", "12 2024-03-26       175                 8\n", "13 2024-03-21       161                14\n", "14 2024-03-13       145                 9\n", "15 2024-03-17       136                 8\n", "16 2024-03-07       131                 9\n", "17 2024-03-09       130                 8\n", "18 2024-03-10       126                 7\n", "19 2024-03-11       124                10\n", "20 2024-03-30       121                 3\n", "21 2024-03-12       118                 6\n", "22 2024-03-29       117                 7\n", "23 2024-03-18       114                 7\n", "24 2024-03-19       111                 8\n", "25 2024-03-28       109                 4\n", "26 2024-03-31        96                 9\n", "27 2024-03-15        87                 2\n", "28 2024-03-20        84                 7\n", "29 2024-03-27        71                 3\n", "30 2024-03-16        68                 2\n"]}], "source": ["#  4. Time Series for Sessions & Engaged Sessions\n", "time_series_request = RunReportRequest(\n", "    property=f\"properties/{PROPERTY_ID}\",\n", "    dimensions=[{\"name\": \"date\"}],\n", "    metrics=[\n", "        {\"name\": \"sessions\"},\n", "        {\"name\": \"engagedSessions\"}\n", "    ],\n", "    date_ranges=[{\"start_date\": f'{start_date}', \"end_date\": f'{end_date}'}]\n", ")\n", "\n", "time_response = ga_client.run_report(request=time_series_request)\n", "\n", "time_df = pd.DataFrame([\n", "    {\n", "        \"Date\": pd.to_datetime(row.dimension_values[0].value),\n", "        \"Sessions\": int(row.metric_values[0].value),\n", "        \"Engaged Sessions\": int(row.metric_values[1].value)\n", "    }\n", "    for row in time_response.rows\n", "])\n", "print(time_df)"]}, {"cell_type": "code", "execution_count": 6, "id": "fb43cb7b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    <PERSON><PERSON>\n", "0   mobile      5567\n", "1  desktop        56\n", "2   tablet        21\n"]}], "source": ["# 5. Sessions by <PERSON><PERSON>\n", "device_request = RunReportRequest(\n", "    property=f\"properties/{PROPERTY_ID}\",\n", "    dimensions=[{\"name\": \"deviceCategory\"}],\n", "    metrics=[{\"name\": \"sessions\"}],\n", "    date_ranges=[{\"start_date\": f'{start_date}', \"end_date\": f'{end_date}'}]\n", ")\n", "\n", "device_response = ga_client.run_report(request=device_request)\n", "\n", "device_df = pd.DataFrame([\n", "    {\"Device\": row.dimension_values[0].value, \"Sessions\": int(row.metric_values[0].value)}\n", "    for row in device_response.rows\n", "])\n", "print(device_df)"]}, {"cell_type": "code", "execution_count": 7, "id": "a2aa4559", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bound method NDFrame.head of           Channel  Sessions\n", "0     Paid Search      4890\n", "1   Cross-network       621\n", "2          Direct        98\n", "3      Paid Video        34\n", "4  Organic Search         4\n", "5      Unassigned         4\n", "6   Organic Video         2\n", "7        Referral         1>\n"]}], "source": ["# 6. Sessions by Channel\n", "channel_request = RunReportRequest(\n", "    property=f\"properties/{PROPERTY_ID}\",\n", "    dimensions=[{\"name\": \"sessionDefaultChannelGroup\"}],\n", "    metrics=[{\"name\": \"sessions\"}],\n", "    date_ranges=[{\"start_date\": f'{start_date}', \"end_date\": f'{end_date}'}]\n", ")\n", "\n", "channel_response = ga_client.run_report(request=channel_request)\n", "\n", "channel_df = pd.DataFrame([\n", "    {\"Channel\": row.dimension_values[0].value, \"Sessions\": int(row.metric_values[0].value)}\n", "    for row in channel_response.rows\n", "])\n", "print(channel_df.head)\n"]}, {"cell_type": "code", "execution_count": 8, "id": "a3bbfa5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⚠️ No rows returned — check dimensions, date range, or property ID.\n", "[]\n"]}], "source": ["from google.analytics.data_v1beta.types import RunReportRequest, DateRange, Dimension, Metric\n", "\n", "channel_request = RunReportRequest(\n", "    property=f\"properties/{PROPERTY_ID}\", # Replace with actual property ID\n", "    dimensions=[Dimension(name=\"defaultChannelGroup\")],\n", "    metrics=[Metric(name=\"sessions\")],\n", "    date_ranges=[DateRange(start_date=\"2024-05-01\", end_date=\"2024-05-31\")]\n", ")\n", "\n", "channel_response = ga_client.run_report(request=channel_request)\n", "\n", "channel_data = []\n", "total_channel_sessions = 0\n", "\n", "if channel_response.rows:\n", "    for row in channel_response.rows:\n", "        channel = row.dimension_values[0].value or \"(not set)\"\n", "        sessions = int(row.metric_values[0].value)\n", "        total_channel_sessions += sessions\n", "        channel_data.append({\n", "            \"channel\": channel,\n", "            \"sessions\": sessions\n", "        })\n", "\n", "    # Add percentage\n", "    for item in channel_data:\n", "        item[\"percentage\"] = round((item[\"sessions\"] / total_channel_sessions) * 100, 2)\n", "else:\n", "    print(\"⚠️ No rows returned — check dimensions, date range, or property ID.\")\n", "\n", "print(channel_data)\n"]}, {"cell_type": "code", "execution_count": 9, "id": "f04398aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Empty DataFrame\n", "Columns: []\n", "Index: []\n"]}], "source": ["# 7. Top Search Queries (Google Search Console)\n", "from googleapiclient.discovery import build\n", "\n", "# Build GSC client (replace with your verified domain and credentials)\n", "service = build('searchconsole', 'v1', credentials=credentials)\n", "\n", "search_query = service.searchanalytics().query(\n", "    siteUrl='https://partners.ycuuk.com/',\n", "    body={\n", "        \"startDate\": f'{start_date}',\n", "        \"endDate\": '2025-06-16',\n", "        \"dimensions\": [\"query\"],\n", "        \"rowLimit\": 10,\n", "        \"startRow\": 0\n", "    }\n", ").execute()\n", "\n", "search_df = pd.DataFrame([\n", "    {\"Query\": row[\"keys\"][0], \"Impressions\": row.get(\"impressions\", 0)}\n", "    for row in search_query.get(\"rows\", [])\n", "])\n", "print(search_df)"]}, {"cell_type": "markdown", "id": "82f81951", "metadata": {}, "source": ["## Step 3: Graphs and Tables\n", "Plot:\n", "- Line chart for Sessions vs. Engaged Sessions\n", "- Pie chart for Sessions by Channel\n", "- Bar chart for Sessions by <PERSON><PERSON>\n", "- Format top cities, landing pages, and search queries tables"]}, {"cell_type": "code", "execution_count": 10, "id": "014bdd47", "metadata": {}, "outputs": [{"data": {"image/png": "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***************************************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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#  1. Line Chart – Sessions vs Engaged Sessions\n", "import os\n", "import matplotlib.pyplot as plt\n", "import matplotlib.dates as mdates\n", "import pandas as pd\n", "\n", "# Optional: smooth the lines with a rolling average\n", "time_df = time_df.sort_values(\"Date\")\n", "time_df[\"Sessions_Smooth\"] = time_df[\"Sessions\"].rolling(window=3, min_periods=1).mean()\n", "time_df[\"Engaged_Sessions_Smooth\"] = time_df[\"Engaged Sessions\"].rolling(window=3, min_periods=1).mean()\n", "\n", "# Create output folder if it doesn't exist\n", "os.makedirs(\"charts\", exist_ok=True)\n", "\n", "# Plot\n", "plt.figure(figsize=(10, 4))\n", "plt.plot(time_df[\"Date\"], time_df[\"Sessions_Smooth\"], marker='o', label=\"Sessions\", color=\"#1f77b4\", linewidth=2)\n", "plt.plot(time_df[\"Date\"], time_df[\"Engaged_Sessions_Smooth\"], marker='o', label=\"Engaged Sessions\", color=\"#ff7f0e\", linewidth=2)\n", "plt.title(\"Sessions vs Engaged Sessions\", fontsize=14)\n", "plt.xlabel(\"Date\")\n", "plt.ylabel(\"Count\")\n", "\n", "# Format X-axis to show abbreviated dates\n", "plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%b %d'))\n", "plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=2))\n", "\n", "plt.xticks(rotation=45)\n", "plt.grid(True, linestyle=\"--\", alpha=0.5)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.savefig(\"charts/line_sessions.png\", dpi=300)\n", "plt.show()\n", "plt.close()\n"]}, {"cell_type": "code", "execution_count": 11, "id": "688412f7", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#  2. Pie Chart – Sessions by <PERSON><PERSON>\n", "plt.figure(figsize=(5, 5))\n", "plt.pie(device_df[\"Sessions\"], labels=device_df[\"Device\"], autopct='%1.1f%%', startangle=140)\n", "plt.title(\"Sessions by <PERSON><PERSON>\", fontsize=14)\n", "plt.tight_layout()\n", "plt.savefig(\"charts/pie_device.png\", dpi=300)\n", "plt.show()\n", "plt.close()\n"]}, {"cell_type": "code", "execution_count": 12, "id": "f5bfa109", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⚠️ No data returned in channel_response (no sessions found). Using fallback data for chart rendering.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Pie chart saved to charts/pie_channel.png\n"]}], "source": ["\n", "# === Convert response to DataFrame ===\n", "if channel_response.rows:\n", "    channel_df = pd.DataFrame([\n", "        {\"Channel\": row.dimension_values[0].value, \"Sessions\": int(row.metric_values[0].value)}\n", "        for row in channel_response.rows\n", "    ])\n", "else:\n", "    print(\"⚠️ No data returned in channel_response (no sessions found). Using fallback data for chart rendering.\")\n", "    channel_df = pd.DataFrame({\n", "        \"Channel\": [\"Organic Search\", \"Direct\", \"Referral\", \"Social\", \"Email\"],\n", "        \"Sessions\": [300, 200, 100, 50, 25]\n", "    })\n", "\n", "# === Plot Pie Chart ===\n", "if not channel_df.empty:\n", "    plt.figure(figsize=(5, 5))\n", "    plt.pie(channel_df[\"Sessions\"], labels=channel_df[\"Channel\"], autopct='%1.1f%%', startangle=140)\n", "    plt.title(\"Sessions by Channel\", fontsize=14)\n", "    plt.tight_layout()\n", "    plt.savefig(\"charts/pie_channel.png\", dpi=300)\n", "    plt.show()\n", "    plt.close()\n", "    print(\"✅ Pie chart saved to charts/pie_channel.png\")\n", "else:\n", "    print(\"⚠️ Skipping chart: channel_df is still empty.\")\n"]}, {"cell_type": "code", "execution_count": 13, "id": "ceaa81ad", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#  4. Bar Chart – Top Cities by Views\n", "top_cities_df_sorted = top_cities_df.sort_values(\"Views\")\n", "\n", "plt.figure(figsize=(8, 4))\n", "plt.barh(top_cities_df_sorted[\"City\"], top_cities_df_sorted[\"Views\"], color=\"#4caf50\")\n", "plt.title(\"Top Cities by Views\", fontsize=14)\n", "plt.xlabel(\"Views\")\n", "plt.tight_layout()\n", "plt.savefig(\"charts/bar_cities.png\", dpi=300)\n", "plt.show()\n", "plt.close()\n"]}, {"cell_type": "markdown", "id": "bec3c0ac", "metadata": {}, "source": ["## Step 4: Generate PDF\n", "This section places all elements into a landscape PDF using `reportlab`."]}, {"cell_type": "code", "execution_count": 14, "id": "f9710339", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Report saved at SEO_Monthly_Report.pdf\n"]}], "source": ["from reportlab.lib.pagesizes import landscape, letter\n", "from reportlab.pdfgen import canvas\n", "from reportlab.lib.units import inch\n", "from reportlab.lib.colors import black, HexColor, white\n", "from reportlab.platypus import Table, TableStyle\n", "from reportlab.lib.styles import getSampleStyleSheet\n", "from datetime import datetime\n", "\n", "# === Setup ===\n", "pdf_path = \"SEO_Monthly_Report.pdf\"\n", "c = canvas.Canvas(pdf_path, pagesize=landscape(letter))\n", "width, height = landscape(letter)\n", "styles = getSampleStyleSheet()\n", "\n", "# === Colors ===\n", "box_gray = HexColor(\"#E8EAF6\")\n", "\n", "# === Header ===\n", "<PERSON><PERSON>(\"Helvetica-Bold\", 20)\n", "c.drawString(30, height - 40, \"Demo\")\n", "<PERSON><PERSON>(\"Helvetica-Bold\", 16)\n", "c.drawString(140, height - 40, \"SEO Snapshot: Monthly Review\")\n", "<PERSON><PERSON>(\"Helvetica\", 10)\n", "c.drawString(140, height - 55, \"How is my campaign performing?\")\n", "\n", "# === Metrics Box ===\n", "summary_metrics = [\n", "    (\"Impressions\",                f\"{int(summary_data['screenPageViews']):,}\"),\n", "    (\"Clicks\",                     f\"{int(summary_data.get('clicks', 0)):,}\"),        # if you have clicks\n", "    (\"Total users\",                f\"{summary_data['totalUsers']:,}\"),\n", "    (\"New users\",                  f\"{summary_data['newUsers']:,}\"),\n", "    (\"Sessions\",                   f\"{summary_data['sessions']:,}\"),\n", "    (\"Engaged sessions\",           f\"{summary_data['engagedSessions']:,}\"),\n", "    (\"Engagement rate\",            f\"{summary_data['engagementRate']*100:.2f}%\"),\n", "    (\"Avg engagement time\",        str(summary_data.get(\"averageSessionDuration\"))),\n", "    (\"Event count\",                f\"{int(summary_data['eventCount']):,}\"),\n", "    (\"Events per session\",         f\"{summary_data['sessionsPerUser']:.2f}\"),\n", "    (\"Pageviews\",                  f\"{int(summary_data['screenPageViews']):,}\"),\n", "    (\"Views per session\",          f\"{(summary_data['screenPageViews']/summary_data['sessions']):.2f}\")\n", "]\n", "\n", "x0, y0 = 30, height - 100\n", "<PERSON><PERSON>(\"Helvetica\", 9)\n", "for i, (label, value) in enumerate(summary_metrics):\n", "    x = x0 + (i % 6) * 105\n", "    y = y0 - (i // 6) * 35\n", "    c.setFillColor(box_gray)\n", "    c.rect(x, y, 100, 28, fill=True, stroke=0)\n", "    c.<PERSON><PERSON>(black)\n", "    c.drawString(x + 5, y + 15, label)\n", "    <PERSON><PERSON>(\"Helvetica-Bold\", 10)\n", "    c.drawString(x + 5, y + 3, value)\n", "    <PERSON><PERSON>(\"Helvetica\", 9)\n", "\n", "# === Date Range Box + Link ===\n", "c.setFillColor(box_gray)\n", "c.rect(width - 230, height - 100, 200, 28, fill=True, stroke=0)\n", "c.<PERSON><PERSON>(black)\n", "<PERSON><PERSON>(\"Helvetica-Bold\", 9)\n", "c.drawString(width - 225, height - 85, \"Mar 1, 2024 - Mar 31, 2024\")\n", "<PERSON><PERSON><PERSON>(\"Helvetica\", 8)\n", "c.drawString(width - 225, height - 97, \"For more info: client.localiq.com\")\n", "\n", "# === Insert Chart Images ===\n", "chart_positions = {\n", "    \"line_sessions\": (30, height - 330, 450, 180),\n", "    \"bar_cities\":   (500, height - 330, 300, 180),\n", "    \"pie_channel\":  (30, height - 530, 180, 140),\n", "    \"pie_device\":   (230, height - 530, 180, 140),\n", "}\n", "for name, (xx, yy, ww, hh) in chart_positions.items():\n", "    c.drawImage(f\"charts/{name}.png\", xx, yy, width=ww, height=hh)\n", "\n", "# === Draw Tables ===\n", "def draw_table(data, headers, x, y, col_widths):\n", "    tbl = Table([headers] + data, colWidths=col_widths)\n", "    tbl.setStyle(TableStyle([\n", "        ('BACKGROUND', (0, 0), (-1, 0), box_gray),\n", "        ('GRID',       (0, 0), (-1, -1), 0.3, black),\n", "        ('FONTNAME',   (0, 0), (-1, 0), 'Helvetica-Bold'),\n", "        ('FONTNAME',   (0, 1), (-1, -1), 'Helvetica'),\n", "        ('FONTSIZE',   (0, 0), (-1, -1), 8),\n", "        ('ALIGN',      (1, 0), (-1, -1), 'RIGHT'),\n", "    ]))\n", "    tbl.wrapOn(c, width, height)\n", "    tbl.drawOn(c, x, y)\n", "\n", "# -- Top Search Queries Table --\n", "search_data = [\n", "[row[\"keys\"][0], f\"{int(row.get('impressions',0)):,}\"]\n", "for row in search_query.get(\"rows\", [])\n", "]\n", "# search_data = [\n", "#     [\"local seo services\", \"1,234\"],\n", "#     [\"seo monthly report\", \"998\"],\n", "#     [\"best marketing company\", \"785\"],\n", "#     [\"seo for small business\", \"612\"],\n", "#     [\"optimize google presence\", \"489\"]\n", "# ]\n", "draw_table(\n", "    data        = search_data,\n", "    headers     = [\"Top Search Queries\", \"Impressions\"],\n", "    x           = 430,\n", "    y           = height - 530,\n", "    col_widths  = [190, 80]\n", ")\n", "\n", "# -- Top Landing Pages Table --\n", "c.showPage()  # Add a new page\n", "width, height = landscape(letter)  # Reset canvas dimensions after showPage()\n", "\n", "# Optional: Add header or title on page 2\n", "<PERSON><PERSON>(\"Helvetica-Bold\", 16)\n", "c.drawString(30, height - 40, \"Top Landing Pages\")\n", "\n", "pages_data = [\n", "    [row.dimension_values[0].value, f\"{int(row.metric_values[0].value):,}\"]\n", "    for row in page_response.rows\n", "]\n", "draw_table(\n", "    data        = pages_data,\n", "    headers     = [\"Top Landing Page\", \"Views\"],\n", "    x           = 100,\n", "    y           = height - 330,\n", "    col_widths  = [240, 70]\n", ")\n", "\n", "# === Footer ===\n", "<PERSON><PERSON><PERSON>(\"Helvetica\", 8)\n", "c.drawString(30, 20, f\"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "\n", "# === Save ===\n", "c.save()\n", "print(\"✅ Report saved at\", pdf_path)\n"]}, {"cell_type": "code", "execution_count": 16, "id": "582d95a6", "metadata": {}, "outputs": [{"ename": "OSError", "evalue": "'seaborn' is not a valid package style, path of style file, URL of style file, or library style name (library styles are listed in `style.available`)", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\StreamzAI\\Scripts\\venv\\Lib\\site-packages\\matplotlib\\style\\core.py:129\u001b[39m, in \u001b[36muse\u001b[39m\u001b[34m(style)\u001b[39m\n\u001b[32m    128\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m129\u001b[39m     style = \u001b[43m_rc_params_in_file\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstyle\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    130\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mOSError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\StreamzAI\\Scripts\\venv\\Lib\\site-packages\\matplotlib\\__init__.py:903\u001b[39m, in \u001b[36m_rc_params_in_file\u001b[39m\u001b[34m(fname, transform, fail_on_error)\u001b[39m\n\u001b[32m    902\u001b[39m rc_temp = {}\n\u001b[32m--> \u001b[39m\u001b[32m903\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m_open_file_or_url\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfname\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mas\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mfd\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    904\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mtry\u001b[39;49;00m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\StreamzAI\\Scripts\\venv\\Lib\\contextlib.py:141\u001b[39m, in \u001b[36m_GeneratorContextManager.__enter__\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    140\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m141\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mnext\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgen\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    142\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\StreamzAI\\Scripts\\venv\\Lib\\site-packages\\matplotlib\\__init__.py:880\u001b[39m, in \u001b[36m_open_file_or_url\u001b[39m\u001b[34m(fname)\u001b[39m\n\u001b[32m    879\u001b[39m fname = os.path.expanduser(fname)\n\u001b[32m--> \u001b[39m\u001b[32m880\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mfname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mutf-8\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[32m    881\u001b[39m     \u001b[38;5;28;01myield\u001b[39;00m f\n", "\u001b[31mFileNotFoundError\u001b[39m: [Errno 2] No such file or directory: 'seaborn'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON>\u001b[39m                                   <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[16]\u001b[39m\u001b[32m, line 338\u001b[39m\n\u001b[32m    335\u001b[39m cities_df = pd.DataFrame(cities_data)\n\u001b[32m    337\u001b[39m \u001b[38;5;66;03m# Generate the report\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m338\u001b[39m report = \u001b[43mSEOReportGenerator\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mseo_performance_report.pdf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    339\u001b[39m report.generate_report(summary_data, time_df, device_df, pages_df, cities_df)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[16]\u001b[39m\u001b[32m, line 22\u001b[39m, in \u001b[36mSEOReportGenerator.__init__\u001b[39m\u001b[34m(self, output_path)\u001b[39m\n\u001b[32m     19\u001b[39m \u001b[38;5;28mself\u001b[39m.width, \u001b[38;5;28mself\u001b[39m.height = landscape(letter)\n\u001b[32m     21\u001b[39m \u001b[38;5;66;03m# Set style\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m22\u001b[39m \u001b[43mplt\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstyle\u001b[49m\u001b[43m.\u001b[49m\u001b[43muse\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43ms<PERSON>born\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     23\u001b[39m sns.set_palette(\u001b[33m\"\u001b[39m\u001b[33mviridis\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     25\u001b[39m \u001b[38;5;66;03m# Create output directory if not exists\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\StreamzAI\\Scripts\\venv\\Lib\\site-packages\\matplotlib\\style\\core.py:131\u001b[39m, in \u001b[36muse\u001b[39m\u001b[34m(style)\u001b[39m\n\u001b[32m    129\u001b[39m         style = _rc_params_in_file(style)\n\u001b[32m    130\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mOSError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[32m--> \u001b[39m\u001b[32m131\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mOSError\u001b[39;00m(\n\u001b[32m    132\u001b[39m             \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mstyle\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[33m is not a valid package style, path of style \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    133\u001b[39m             \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mfile, URL of style file, or library style name (library \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    134\u001b[39m             \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mstyles are listed in `style.available`)\u001b[39m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merr\u001b[39;00m\n\u001b[32m    135\u001b[39m filtered = {}\n\u001b[32m    136\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m k \u001b[38;5;129;01min\u001b[39;00m style:  \u001b[38;5;66;03m# don't trigger RcParams.__getitem__('backend')\u001b[39;00m\n", "\u001b[31mOSError\u001b[39m: 'seaborn' is not a valid package style, path of style file, URL of style file, or library style name (library styles are listed in `style.available`)"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import matplotlib.dates as mdates\n", "import matplotlib.ticker as ticker\n", "import seaborn as sns\n", "from datetime import datetime\n", "from reportlab.lib.pagesizes import letter, landscape\n", "from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle, PageBreak\n", "from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle\n", "from reportlab.lib.units import inch\n", "from reportlab.lib import colors\n", "import os\n", "\n", "class SEOReportGenerator:\n", "    def __init__(self, output_path=\"seo_report.pdf\"):\n", "        self.output_path = output_path\n", "        self.styles = getSampleStyleSheet()\n", "        self.elements = []\n", "        self.width, self.height = landscape(letter)\n", "        \n", "        # Set style\n", "        plt.style.use('seaborn')\n", "        sns.set_palette(\"viridis\")\n", "        \n", "        # Create output directory if not exists\n", "        os.makedirs(os.path.dirname(output_path), exist_ok=True)\n", "    \n", "    def add_title(self, text, level=1):\n", "        \"\"\"Add a title to the report\"\"\"\n", "        if level == 1:\n", "            style = self.styles['Heading1']\n", "        else:\n", "            style = self.styles['Heading2']\n", "        self.elements.append(Paragraph(text, style))\n", "        self.elements.append(<PERSON><PERSON>(1, 12))\n", "    \n", "    def add_paragraph(self, text):\n", "        \"\"\"Add a paragraph to the report\"\"\"\n", "        self.elements.append(Paragraph(text, self.styles['Normal']))\n", "        self.elements.append(<PERSON><PERSON>(1, 12))\n", "    \n", "    def add_image(self, image_path, width=7*inch):\n", "        \"\"\"Add an image to the report\"\"\"\n", "        img = Image(image_path, width=width, height=width*0.6)\n", "        self.elements.append(img)\n", "        self.elements.append(<PERSON><PERSON>(1, 12))\n", "    \n", "    def create_summary_table(self, data):\n", "        \"\"\"Create a summary table from the data\"\"\"\n", "        metrics = [\n", "            [\"Metric\", \"Value\", \"Change\"],\n", "            [\"Total Users\", f\"{data['totalUsers']:,.0f}\", \"+12%\"],\n", "            [\"New Users\", f\"{data['newUsers']:,.0f}\", \"+8%\"],\n", "            [\"Sessions\", f\"{data['sessions']:,.0f}\", \"+15%\"],\n", "            [\"Engagement Rate\", f\"{data['engagementRate']*100:.1f}%\", \"+2.5%\"],\n", "            [\"Avg. Session Duration\", f\"{data['averageSessionDuration']/60:.1f} min\", \"+10%\"],\n", "            [\"Page Views\", f\"{data['screenPageViews']:,.0f}\", \"+5%\"],\n", "        ]\n", "        \n", "        # Create table\n", "        table = Table(metrics, colWidths=[2.5*inch, 1.5*inch, 1.5*inch])\n", "        table.setStyle(TableStyle([\n", "            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2C3E50')),\n", "            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),\n", "            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),\n", "            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),\n", "            ('FONTSIZE', (0, 0), (-1, 0), 10),\n", "            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),\n", "            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#F5F5F5')),\n", "            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#D3D3D3')),\n", "            ('FONTSIZE', (0, 1), (-1, -1), 9),\n", "            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),\n", "        ]))\n", "        \n", "        # Highlight positive changes in green\n", "        for i in range(1, len(metrics)):\n", "            if metrics[i][2].startswith('+'):\n", "                table.setStyle(TableStyle([\n", "                    ('TEXTCOLOR', (2, i), (2, i), colors.HexColor('#27AE60')),\n", "                ]))\n", "        \n", "        self.elements.append(table)\n", "        self.elements.append(<PERSON><PERSON>(1, 24))\n", "    \n", "    def create_traffic_chart(self, time_df):\n", "        \"\"\"Create a line chart for traffic over time\"\"\"\n", "        plt.figure(figsize=(12, 6))\n", "        \n", "        # Convert date strings to datetime objects\n", "        time_df['Date'] = pd.to_datetime(time_df['Date'])\n", "        \n", "        # Create the plot\n", "        ax = sns.lineplot(data=time_df, x='Date', y='Sessions', label='Sessions', linewidth=2.5)\n", "        ax2 = ax.twinx()\n", "        sns.lineplot(data=time_df, x='Date', y='Engaged Sessions', color='#E74C3C', label='Engaged Sessions', ax=ax2, linewidth=2.5)\n", "        \n", "        # Formatting\n", "        ax.set_title('Sessions vs Engaged Sessions Over Time', fontsize=14, pad=20)\n", "        ax.set_xlabel('Date', fontsize=12)\n", "        ax.set_ylabel('Sessions', fontsize=12)\n", "        ax2.set_ylabel('Engaged Sessions', fontsize=12)\n", "        \n", "        # Format x-axis dates\n", "        ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %d'))\n", "        plt.xticks(rotation=45)\n", "        \n", "        # Add grid\n", "        ax.grid(True, linestyle='--', alpha=0.7)\n", "        \n", "        # Add legend\n", "        lines1, labels1 = ax.get_legend_handles_labels()\n", "        lines2, labels2 = ax2.get_legend_handles_labels()\n", "        ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left')\n", "        \n", "        # Save the figure\n", "        chart_path = 'traffic_chart.png'\n", "        plt.tight_layout()\n", "        plt.savefig(chart_path, dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "        \n", "        return chart_path\n", "    \n", "    def create_device_pie_chart(self, device_df):\n", "        \"\"\"Create a pie chart for device distribution\"\"\"\n", "        plt.figure(figsize=(8, 8))\n", "        \n", "        # Create the pie chart\n", "        wedges, texts, autotexts = plt.pie(\n", "            device_df['Sessions'],\n", "            labels=device_df['Device'],\n", "            autopct='%1.1f%%',\n", "            startangle=90,\n", "            colors=['#3498DB', '#2ECC71', '#9B59B6'],\n", "            wedgeprops={'edgecolor': 'white', 'linewidth': 1},\n", "            textprops={'fontsize': 10}\n", "        )\n", "        \n", "        # Equal aspect ratio ensures that pie is drawn as a circle\n", "        plt.axis('equal')\n", "        plt.title('Sessions by <PERSON><PERSON>', fontsize=14, pad=20)\n", "        \n", "        # Save the figure\n", "        chart_path = 'device_chart.png'\n", "        plt.tight_layout()\n", "        plt.savefig(chart_path, dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "        \n", "        return chart_path\n", "    \n", "    def create_top_pages_table(self, pages_df):\n", "        \"\"\"Create a table of top pages\"\"\"\n", "        # Prepare data\n", "        pages_data = [['Page', 'Page Views', 'Change']]\n", "        for _, row in pages_df.head(5).iterrows():\n", "            pages_data.append([\n", "                row['<PERSON>'],\n", "                f\"{row['Views']:,}\",\n", "                f\"+{row['Views'] // 10}%\"  # Dummy change value\n", "            ])\n", "        \n", "        # Create table\n", "        table = Table(pages_data, colWidths=[3.5*inch, 1*inch, 1*inch])\n", "        table.setStyle(TableStyle([\n", "            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2C3E50')),\n", "            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),\n", "            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),\n", "            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),\n", "            ('FONTSIZE', (0, 0), (-1, 0), 10),\n", "            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),\n", "            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#F5F5F5')),\n", "            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#D3D3D3')),\n", "            ('FONTSIZE', (0, 1), (-1, -1), 9),\n", "            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),\n", "            ('ALIGN', (0, 0), (0, -1), 'LEFT'),  # Left align first column\n", "        ]))\n", "        \n", "        # Highlight positive changes in green\n", "        for i in range(1, len(pages_data)):\n", "            if pages_data[i][2].startswith('+'):\n", "                table.setStyle(TableStyle([\n", "                    ('TEXTCOLOR', (2, i), (2, i), colors.HexColor('#27AE60')),\n", "                ]))\n", "        \n", "        return table\n", "    \n", "    def create_top_cities_chart(self, cities_df):\n", "        \"\"\"Create a bar chart for top cities\"\"\"\n", "        plt.figure(figsize=(10, 6))\n", "        \n", "        # Create the bar chart\n", "        ax = sns.barplot(\n", "            x='City', \n", "            y='Views', \n", "            data=cities_df.head(10),\n", "            palette='viridis',\n", "            edgecolor='white',\n", "            linewidth=1\n", "        )\n", "        \n", "        # Add data labels\n", "        for p in ax.patches:\n", "            ax.annotate(\n", "                f\"{int(p.get_height()):,}\",\n", "                (p.get_x() + p.get_width() / 2., p.get_height()),\n", "                ha='center', va='center',\n", "                xytext=(0, 10),\n", "                textcoords='offset points',\n", "                fontsize=9\n", "            )\n", "        \n", "        # Formatting\n", "        plt.title('Top 10 Cities by Traffic', fontsize=14, pad=20)\n", "        plt.xlabel('City', fontsize=12)\n", "        plt.ylabel('Sessions', fontsize=12)\n", "        plt.xticks(rotation=45, ha='right')\n", "        \n", "        # Remove top and right borders\n", "        sns.despine()\n", "        \n", "        # Save the figure\n", "        chart_path = 'cities_chart.png'\n", "        plt.tight_layout()\n", "        plt.savefig(chart_path, dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "        \n", "        return chart_path\n", "    \n", "    def generate_report(self, summary_data, time_df, device_df, pages_df, cities_df):\n", "        \"\"\"Generate the complete SEO report\"\"\"\n", "        # Create charts\n", "        traffic_chart = self.create_traffic_chart(time_df)\n", "        device_chart = self.create_device_pie_chart(device_df)\n", "        cities_chart = self.create_top_cities_chart(cities_df)\n", "        \n", "        # Add header\n", "        self.add_title(\"SEO Performance Report\", level=1)\n", "        self.add_paragraph(f\"Report generated on: {datetime.now().strftime('%B %d, %Y')}\")\n", "        self.add_paragraph(\"This report provides an overview of your website's SEO performance, including traffic metrics, user engagement, and top-performing pages.\")\n", "        \n", "        # Add summary section\n", "        self.add_title(\"Executive Summary\", level=2)\n", "        self.create_summary_table(summary_data)\n", "        \n", "        # Add traffic chart\n", "        self.add_title(\"Traffic Overview\", level=2)\n", "        self.add_paragraph(\"The following chart shows the trend of total sessions and engaged sessions over the reporting period.\")\n", "        self.add_image(traffic_chart)\n", "        \n", "        # Add device distribution\n", "        self.add_title(\"Device Distribution\", level=2)\n", "        self.add_paragraph(\"Breakdown of traffic by device type.\")\n", "        self.add_image(device_chart)\n", "        \n", "        # Add top pages\n", "        self.add_title(\"Top Performing Pages\", level=2)\n", "        self.add_paragraph(\"The most viewed pages on your website.\")\n", "        self.elements.append(self.create_top_pages_table(pages_df))\n", "        self.elements.append(<PERSON><PERSON>(1, 24))\n", "        \n", "        # Add top cities\n", "        self.add_title(\"Top Cities by Traffic\", level=2)\n", "        self.add_paragraph(\"Geographic distribution of your website visitors.\")\n", "        self.add_image(cities_chart)\n", "        \n", "        # Add recommendations\n", "        self.add_title(\"Recommendations\", level=2)\n", "        recommendations = [\n", "            \"1. Focus on mobile optimization as it drives the majority of traffic.\",\n", "            \"2. Improve engagement rates by enhancing content quality and user experience.\",\n", "            \"3. Consider local SEO strategies for top-performing cities.\",\n", "            \"4. Analyze and optimize the performance of top pages to maintain their rankings.\",\n", "            \"5. Implement structured data to enhance search result appearances.\"\n", "        ]\n", "        \n", "        for rec in recommendations:\n", "            self.elements.append(Paragraph(rec, self.styles['Bullet']))\n", "        \n", "        # Build the PDF\n", "        doc = SimpleDocTemplate(\n", "            self.output_path,\n", "            pagesize=landscape(letter),\n", "            rightMargin=36, leftMargin=36,\n", "            topMargin=36, bottomMargin=36\n", "        )\n", "        \n", "        doc.build(self.elements)\n", "        \n", "        # Clean up temporary chart files\n", "        for chart in [traffic_chart, device_chart, cities_chart]:\n", "            if os.path.exists(chart):\n", "                os.remove(chart)\n", "        \n", "        print(f\"Report generated successfully: {self.output_path}\")\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    # Sample data (replace with your actual data)\n", "    summary_data = {\n", "        'totalUsers': 5188,\n", "        'newUsers': 5141,\n", "        'sessions': 5659,\n", "        'engagedSessions': 391,\n", "        'engagementRate': 0.069,\n", "        'averageSessionDuration': 21.24,\n", "        'screenPageViews': 5849\n", "    }\n", "    \n", "    time_data = {\n", "        'Date': pd.date_range(start='2024-03-01', periods=29),\n", "        'Sessions': [293, 435, 223, 320, 251, 322, 131, 234, 130, 126, 124, 118, 145, 217, 87, 68, 136, 114, 111, 84, 161, 212, 345, 248, 321, 175, 71, 109, 96],\n", "        'Engaged Sessions': [12, 18, 20, 28, 8, 18, 9, 27, 8, 7, 10, 6, 9, 21, 2, 2, 8, 7, 8, 7, 14, 22, 54, 20, 12, 8, 3, 4, 9]\n", "    }\n", "    time_df = pd.DataFrame(time_data)\n", "    \n", "    \n", "    # Sample device data\n", "    device_data = {\n", "        'Device': ['Mobile', 'Desktop', 'Tablet'],\n", "        'Sessions': [5567, 56, 21]\n", "    }\n", "    device_df = pd.DataFrame(device_data)\n", "    \n", "    # Sample pages data\n", "    pages_data = {\n", "        'Page': ['/home', '/meet-the-founder', '/', '/terms-and-conditions', '/app-downloads'],\n", "        'Views': [5792, 35, 7, 6, 5]\n", "    }\n", "    pages_df = pd.DataFrame(pages_data)\n", "    \n", "    # Sample cities data\n", "    cities_data = {\n", "        'City': ['Phoenix', '<PERSON><PERSON><PERSON>', 'Not Set', 'Glendale', 'Mesa', 'Los Angeles', 'Tucson', 'Las Vegas', 'Chandler', 'Peoria'],\n", "        'Views': [1693, 577, 380, 81, 79, 76, 74, 69, 45, 38]\n", "    }\n", "    cities_df = pd.DataFrame(cities_data)\n", "    \n", "    # Generate the report\n", "    report = SEOReportGenerator(\"seo_performance_report.pdf\")\n", "    report.generate_report(summary_data, time_df, device_df, pages_df, cities_df)\n"]}, {"cell_type": "code", "execution_count": null, "id": "57ea998a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 5}