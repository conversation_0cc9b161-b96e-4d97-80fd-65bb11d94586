body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #fff;
    color: #222;
  }
  
  .report-container {
    width: 100%;
    max-width: 1100px;
    margin: auto;
  }
  
  header {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  
  .logo {
    width: 120px;
  }
  
  h1 {
    margin: 0;
    font-size: 22px;
  }
  
  .meta-info {
    margin: 10px 0 20px;
    font-size: 13px;
  }
  
  .top-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 10px;
    margin-bottom: 30px;
  }
  
  .kpi {
    background: #f2f2f2;
    padding: 10px;
    border-left: 5px solid #0077cc;
    font-size: 14px;
  }
  
  .kpi span {
    display: block;
    font-size: 18px;
    font-weight: bold;
  }
  
  .charts {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
  }
  
  .charts canvas {
    background: #fff;
    border: 1px solid #ccc;
    padding: 10px;
    width: 100%;
    max-width: 500px;
    height: 300px;
  }
  
  .tables {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }
  
  .table-box {
    background: #f9f9f9;
    padding: 10px;
  }
  
  table {
    width: 100%;
    font-size: 13px;
    border-collapse: collapse;
  }
  
  th, td {
    padding: 6px;
    border-bottom: 1px solid #ccc;
    text-align: left;
  }
  