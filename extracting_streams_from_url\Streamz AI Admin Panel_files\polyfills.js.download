"use strict";(self.webpackChunkts_angular=self.webpackChunkts_angular||[]).push([[461],{12523:(Me,Re,ft)=>{const Ye=":";Error;const I=function(e,...t){if(I.translate){const d=I.translate(e,t);e=d[0],t=d[1]}let o=k(e[0],e.raw[0]);for(let d=1;d<e.length;d++)o+=t[d-1]+k(e[d],e.raw[d]);return o},W=":";function k(e,t){return t.charAt(0)===W?e.substring(function i(e,t){for(let o=1,d=1;o<e.length;o++,d++)if("\\"===t[d])d++;else if(e[o]===Ye)return o;throw new Error(`Unterminated $localize metadata block in "${t}".`)}(e,t)+1):e}globalThis.$localize=I,ft(96935),ft(70542)},70542:()=>{const Ye=globalThis;function Ve(C){return(Ye.__Zone_symbol_prefix||"__zone_symbol__")+C}const rt=typeof window<"u"&&window||typeof self<"u"&&self||global;class Ke{static get symbolParentUnresolved(){return Ve("parentUnresolved")}constructor(n,s,u){this.finishCallback=n,this.failCallback=s,this._pendingMicroTasks=!1,this._pendingMacroTasks=!1,this._alreadyErrored=!1,this._isSync=!1,this._existingFinishTimer=null,this.entryFunction=null,this.runZone=Zone.current,this.unresolvedChainedPromiseCount=0,this.supportWaitUnresolvedChainedPromise=!1,this.name="asyncTestZone for "+u,this.properties={AsyncTestZoneSpec:this},this.supportWaitUnresolvedChainedPromise=!0===rt[Ve("supportWaitUnResolvedChainedPromise")]}isUnresolvedChainedPromisePending(){return this.unresolvedChainedPromiseCount>0}_finishCallbackIfDone(){null!==this._existingFinishTimer&&(clearTimeout(this._existingFinishTimer),this._existingFinishTimer=null),this._pendingMicroTasks||this._pendingMacroTasks||this.supportWaitUnresolvedChainedPromise&&this.isUnresolvedChainedPromisePending()||this.runZone.run(()=>{this._existingFinishTimer=setTimeout(()=>{!this._alreadyErrored&&!this._pendingMicroTasks&&!this._pendingMacroTasks&&this.finishCallback()},0)})}patchPromiseForTest(){if(!this.supportWaitUnresolvedChainedPromise)return;const n=Promise[Zone.__symbol__("patchPromiseForTest")];n&&n()}unPatchPromiseForTest(){if(!this.supportWaitUnresolvedChainedPromise)return;const n=Promise[Zone.__symbol__("unPatchPromiseForTest")];n&&n()}onScheduleTask(n,s,u,i){return"eventTask"!==i.type&&(this._isSync=!1),"microTask"===i.type&&i.data&&i.data instanceof Promise&&!0===i.data[Ke.symbolParentUnresolved]&&this.unresolvedChainedPromiseCount--,n.scheduleTask(u,i)}onInvokeTask(n,s,u,i,w,v){return"eventTask"!==i.type&&(this._isSync=!1),n.invokeTask(u,i,w,v)}onCancelTask(n,s,u,i){return"eventTask"!==i.type&&(this._isSync=!1),n.cancelTask(u,i)}onInvoke(n,s,u,i,w,v,R){this.entryFunction||(this.entryFunction=i);try{return this._isSync=!0,n.invoke(u,i,w,v,R)}finally{this._isSync&&this.entryFunction===i&&this._finishCallbackIfDone()}}onHandleError(n,s,u,i){return n.handleError(u,i)&&(this.failCallback(i),this._alreadyErrored=!0),!1}onHasTask(n,s,u,i){n.hasTask(u,i),s===u&&("microTask"==i.change?(this._pendingMicroTasks=i.microTask,this._finishCallbackIfDone()):"macroTask"==i.change&&(this._pendingMacroTasks=i.macroTask,this._finishCallbackIfDone()))}}const le="object"==typeof window&&window||"object"==typeof self&&self||globalThis.global,Fe=le.Date;function Le(){if(0===arguments.length){const C=new Fe;return C.setTime(Le.now()),C}{const C=Array.prototype.slice.call(arguments);return new Fe(...C)}}let xe;Le.now=function(){const C=Zone.current.get("FakeAsyncTestZoneSpec");return C?C.getFakeSystemTime():Fe.now.apply(this,arguments)},Le.UTC=Fe.UTC,Le.parse=Fe.parse;const dt=function(){};let De=(()=>{class C{static{this.nextNodeJSId=1}static{this.nextId=-1}constructor(){this._schedulerQueue=[],this._currentTickTime=0,this._currentFakeBaseSystemTime=Fe.now(),this._currentTickRequeuePeriodicEntries=[]}static getNextId(){const s=xe.nativeSetTimeout.call(le,dt,0);return xe.nativeClearTimeout.call(le,s),"number"==typeof s?s:C.nextNodeJSId++}getCurrentTickTime(){return this._currentTickTime}getFakeSystemTime(){return this._currentFakeBaseSystemTime+this._currentTickTime}setFakeBaseSystemTime(s){this._currentFakeBaseSystemTime=s}getRealSystemTime(){return Fe.now()}scheduleFunction(s,u,i){let w=(i={args:[],isPeriodic:!1,isRequestAnimationFrame:!1,id:-1,isRequeuePeriodic:!1,...i}).id<0?C.nextId:i.id;C.nextId=C.getNextId();let R={endTime:this._currentTickTime+u,id:w,func:s,args:i.args,delay:u,isPeriodic:i.isPeriodic,isRequestAnimationFrame:i.isRequestAnimationFrame};i.isRequeuePeriodic&&this._currentTickRequeuePeriodicEntries.push(R);let S=0;for(;S<this._schedulerQueue.length&&!(R.endTime<this._schedulerQueue[S].endTime);S++);return this._schedulerQueue.splice(S,0,R),w}removeScheduledFunctionWithId(s){for(let u=0;u<this._schedulerQueue.length;u++)if(this._schedulerQueue[u].id==s){this._schedulerQueue.splice(u,1);break}}removeAll(){this._schedulerQueue=[]}getTimerCount(){return this._schedulerQueue.length}tickToNext(s=1,u,i){this._schedulerQueue.length<s||this.tick(this._schedulerQueue[s-1].endTime-this._currentTickTime,u,i)}tick(s=0,u,i){let w=this._currentTickTime+s,v=0;const R=(i=Object.assign({processNewMacroTasksSynchronously:!0},i)).processNewMacroTasksSynchronously?this._schedulerQueue:this._schedulerQueue.slice();if(0===R.length&&u)u(s);else{for(;R.length>0&&(this._currentTickRequeuePeriodicEntries=[],!(w<R[0].endTime));){let re=R.shift();if(!i.processNewMacroTasksSynchronously){const Se=this._schedulerQueue.indexOf(re);Se>=0&&this._schedulerQueue.splice(Se,1)}if(v=this._currentTickTime,this._currentTickTime=re.endTime,u&&u(this._currentTickTime-v),!re.func.apply(le,re.isRequestAnimationFrame?[this._currentTickTime]:re.args))break;i.processNewMacroTasksSynchronously||this._currentTickRequeuePeriodicEntries.forEach(Se=>{let ce=0;for(;ce<R.length&&!(Se.endTime<R[ce].endTime);ce++);R.splice(ce,0,Se)})}v=this._currentTickTime,this._currentTickTime=w,u&&u(this._currentTickTime-v)}}flushOnlyPendingTimers(s){if(0===this._schedulerQueue.length)return 0;const u=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-u,s,{processNewMacroTasksSynchronously:!1}),this._currentTickTime-u}flush(s=20,u=!1,i){return u?this.flushPeriodic(i):this.flushNonPeriodic(s,i)}flushPeriodic(s){if(0===this._schedulerQueue.length)return 0;const u=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-u,s),this._currentTickTime-u}flushNonPeriodic(s,u){const i=this._currentTickTime;let w=0,v=0;for(;this._schedulerQueue.length>0;){if(v++,v>s)throw new Error("flush failed after reaching the limit of "+s+" tasks. Does your code use a polling timeout?");if(0===this._schedulerQueue.filter(re=>!re.isPeriodic&&!re.isRequestAnimationFrame).length)break;const R=this._schedulerQueue.shift();if(w=this._currentTickTime,this._currentTickTime=R.endTime,u&&u(this._currentTickTime-w),!R.func.apply(le,R.args))break}return this._currentTickTime-i}}return C})();class he{static assertInZone(){if(null==Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("The code should be running in the fakeAsync zone to call this function")}constructor(n,s=!1,u){this.trackPendingRequestAnimationFrame=s,this.macroTaskOptions=u,this._scheduler=new De,this._microtasks=[],this._lastError=null,this._uncaughtPromiseErrors=Promise[Zone.__symbol__("uncaughtPromiseErrors")],this.pendingPeriodicTimers=[],this.pendingTimers=[],this.patchDateLocked=!1,this.properties={FakeAsyncTestZoneSpec:this},this.name="fakeAsyncTestZone for "+n,this.macroTaskOptions||(this.macroTaskOptions=le[Zone.__symbol__("FakeAsyncTestMacroTask")])}_fnAndFlush(n,s){return(...u)=>(n.apply(le,u),null===this._lastError?(null!=s.onSuccess&&s.onSuccess.apply(le),this.flushMicrotasks()):null!=s.onError&&s.onError.apply(le),null===this._lastError)}static _removeTimer(n,s){let u=n.indexOf(s);u>-1&&n.splice(u,1)}_dequeueTimer(n){return()=>{he._removeTimer(this.pendingTimers,n)}}_requeuePeriodicTimer(n,s,u,i){return()=>{-1!==this.pendingPeriodicTimers.indexOf(i)&&this._scheduler.scheduleFunction(n,s,{args:u,isPeriodic:!0,id:i,isRequeuePeriodic:!0})}}_dequeuePeriodicTimer(n){return()=>{he._removeTimer(this.pendingPeriodicTimers,n)}}_setTimeout(n,s,u,i=!0){let w=this._dequeueTimer(De.nextId),v=this._fnAndFlush(n,{onSuccess:w,onError:w}),R=this._scheduler.scheduleFunction(v,s,{args:u,isRequestAnimationFrame:!i});return i&&this.pendingTimers.push(R),R}_clearTimeout(n){he._removeTimer(this.pendingTimers,n),this._scheduler.removeScheduledFunctionWithId(n)}_setInterval(n,s,u){let i=De.nextId,w={onSuccess:null,onError:this._dequeuePeriodicTimer(i)},v=this._fnAndFlush(n,w);return w.onSuccess=this._requeuePeriodicTimer(v,s,u,i),this._scheduler.scheduleFunction(v,s,{args:u,isPeriodic:!0}),this.pendingPeriodicTimers.push(i),i}_clearInterval(n){he._removeTimer(this.pendingPeriodicTimers,n),this._scheduler.removeScheduledFunctionWithId(n)}_resetLastErrorAndThrow(){let n=this._lastError||this._uncaughtPromiseErrors[0];throw this._uncaughtPromiseErrors.length=0,this._lastError=null,n}getCurrentTickTime(){return this._scheduler.getCurrentTickTime()}getFakeSystemTime(){return this._scheduler.getFakeSystemTime()}setFakeBaseSystemTime(n){this._scheduler.setFakeBaseSystemTime(n)}getRealSystemTime(){return this._scheduler.getRealSystemTime()}static patchDate(){le[Zone.__symbol__("disableDatePatching")]||le.Date!==Le&&(le.Date=Le,Le.prototype=Fe.prototype,he.checkTimerPatch())}static resetDate(){le.Date===Le&&(le.Date=Fe)}static checkTimerPatch(){if(!xe)throw new Error("Expected timers to have been patched.");le.setTimeout!==xe.setTimeout&&(le.setTimeout=xe.setTimeout,le.clearTimeout=xe.clearTimeout),le.setInterval!==xe.setInterval&&(le.setInterval=xe.setInterval,le.clearInterval=xe.clearInterval)}lockDatePatch(){this.patchDateLocked=!0,he.patchDate()}unlockDatePatch(){this.patchDateLocked=!1,he.resetDate()}tickToNext(n=1,s,u={processNewMacroTasksSynchronously:!0}){n<=0||(he.assertInZone(),this.flushMicrotasks(),this._scheduler.tickToNext(n,s,u),null!==this._lastError&&this._resetLastErrorAndThrow())}tick(n=0,s,u={processNewMacroTasksSynchronously:!0}){he.assertInZone(),this.flushMicrotasks(),this._scheduler.tick(n,s,u),null!==this._lastError&&this._resetLastErrorAndThrow()}flushMicrotasks(){for(he.assertInZone();this._microtasks.length>0;){let s=this._microtasks.shift();s.func.apply(s.target,s.args)}(()=>{(null!==this._lastError||this._uncaughtPromiseErrors.length)&&this._resetLastErrorAndThrow()})()}flush(n,s,u){he.assertInZone(),this.flushMicrotasks();const i=this._scheduler.flush(n,s,u);return null!==this._lastError&&this._resetLastErrorAndThrow(),i}flushOnlyPendingTimers(n){he.assertInZone(),this.flushMicrotasks();const s=this._scheduler.flushOnlyPendingTimers(n);return null!==this._lastError&&this._resetLastErrorAndThrow(),s}removeAllTimers(){he.assertInZone(),this._scheduler.removeAll(),this.pendingPeriodicTimers=[],this.pendingTimers=[]}getTimerCount(){return this._scheduler.getTimerCount()+this._microtasks.length}onScheduleTask(n,s,u,i){switch(i.type){case"microTask":let v,w=i.data&&i.data.args;if(w){let R=i.data.cbIdx;"number"==typeof w.length&&w.length>R+1&&(v=Array.prototype.slice.call(w,R+1))}this._microtasks.push({func:i.invoke,args:v,target:i.data&&i.data.target});break;case"macroTask":switch(i.source){case"setTimeout":i.data.handleId=this._setTimeout(i.invoke,i.data.delay,Array.prototype.slice.call(i.data.args,2));break;case"setImmediate":i.data.handleId=this._setTimeout(i.invoke,0,Array.prototype.slice.call(i.data.args,1));break;case"setInterval":i.data.handleId=this._setInterval(i.invoke,i.data.delay,Array.prototype.slice.call(i.data.args,2));break;case"XMLHttpRequest.send":throw new Error("Cannot make XHRs from within a fake async test. Request URL: "+i.data.url);case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":i.data.handleId=this._setTimeout(i.invoke,16,i.data.args,this.trackPendingRequestAnimationFrame);break;default:const R=this.findMacroTaskOption(i);if(R){const S=i.data&&i.data.args,re=S&&S.length>1?S[1]:0;let ie=R.callbackArgs?R.callbackArgs:S;R.isPeriodic?(i.data.handleId=this._setInterval(i.invoke,re,ie),i.data.isPeriodic=!0):i.data.handleId=this._setTimeout(i.invoke,re,ie);break}throw new Error("Unknown macroTask scheduled in fake async test: "+i.source)}break;case"eventTask":i=n.scheduleTask(u,i)}return i}onCancelTask(n,s,u,i){switch(i.source){case"setTimeout":case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":return this._clearTimeout(i.data.handleId);case"setInterval":return this._clearInterval(i.data.handleId);default:const w=this.findMacroTaskOption(i);if(w){const v=i.data.handleId;return w.isPeriodic?this._clearInterval(v):this._clearTimeout(v)}return n.cancelTask(u,i)}}onInvoke(n,s,u,i,w,v,R){try{return he.patchDate(),n.invoke(u,i,w,v,R)}finally{this.patchDateLocked||he.resetDate()}}findMacroTaskOption(n){if(!this.macroTaskOptions)return null;for(let s=0;s<this.macroTaskOptions.length;s++){const u=this.macroTaskOptions[s];if(u.source===n.source)return u}return null}onHandleError(n,s,u,i){return this._lastError=i,!1}}let ye=null;function et(){return Zone&&Zone.ProxyZoneSpec}function Ue(){ye&&ye.unlockDatePatch(),ye=null,et()&&et().assertPresent().resetDelegate()}function se(C,n={}){const{flush:s=!1}=n,u=function(...i){const w=et();if(!w)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");const v=w.assertPresent();if(Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("fakeAsync() calls can not be nested");try{if(!ye){const re=Zone&&Zone.FakeAsyncTestZoneSpec;if(v.getDelegate()instanceof re)throw new Error("fakeAsync() calls can not be nested");ye=new re}let R;const S=v.getDelegate();v.setDelegate(ye),ye.lockDatePatch();try{R=C.apply(this,i),s?ye.flush(20,!0):We()}finally{v.setDelegate(S)}if(!s){if(ye.pendingPeriodicTimers.length>0)throw new Error(`${ye.pendingPeriodicTimers.length} periodic timer(s) still in the queue.`);if(ye.pendingTimers.length>0)throw new Error(`${ye.pendingTimers.length} timer(s) still in the queue.`)}return R}finally{Ue()}};return u.isFakeAsync=!0,u}function ze(){if(null==ye&&(ye=Zone.current.get("FakeAsyncTestZoneSpec"),null==ye))throw new Error("The code should be running in the fakeAsync zone to call this function");return ye}function qe(C=0,n=!1){ze().tick(C,null,n)}function ge(C){return ze().flush(C)}function _t(){ze().pendingPeriodicTimers.length=0}function We(){ze().flushMicrotasks()}function yt(C){const n="\n",s={},u="__creationTrace__",i="STACKTRACE TRACKING",w="__SEP_TAG__";let v=w+"@[native]";class R{constructor(){this.error=ce(),this.timestamp=new Date}}function S(){return new Error(i)}function re(){try{throw S()}catch(p){return p}}const ie=S(),Se=re(),ce=ie.stack?S:Se.stack?re:S;function ve(p){return p.stack?p.stack.split(n):[]}function U(p,e){let t=ve(e);for(let o=0;o<t.length;o++)s.hasOwnProperty(t[o])||p.push(t[o])}function I(p,e){const t=[e?e.trim():""];if(p){let o=(new Date).getTime();for(let d=0;d<p.length;d++){const E=p[d],D=E.timestamp;let ke=`____________________Elapsed ${o-D.getTime()} ms; At: ${D}`;ke=ke.replace(/[^\w\d]/g,"_"),t.push(v.replace(w,ke)),U(t,E.error),o=D.getTime()}}return t.join(n)}function W(){return Error.stackTraceLimit>0}function k(p,e){e>0&&(p.push(ve((new R).error)),k(p,e-1))}C.longStackTraceZoneSpec={name:"long-stack-trace",longStackTraceLimit:10,getLongStackTrace:function(p){if(!p)return;const e=p[C.__symbol__("currentTaskTrace")];return e?I(e,p.stack):p.stack},onScheduleTask:function(p,e,t,o){if(W()){const d=C.currentTask;let E=d&&d.data&&d.data[u]||[];E=[new R].concat(E),E.length>this.longStackTraceLimit&&(E.length=this.longStackTraceLimit),o.data||(o.data={}),"eventTask"===o.type&&(o.data={...o.data}),o.data[u]=E}return p.scheduleTask(t,o)},onHandleError:function(p,e,t,o){if(W()){const d=C.currentTask||o.task;if(o instanceof Error&&d){const E=I(d.data&&d.data[u],o.stack);try{o.stack=o.longStack=E}catch{}}}return p.handleError(t,o)}},function P(){if(!W())return;const p=[];k(p,2);const e=p[0],t=p[1];for(let o=0;o<e.length;o++){const d=e[o];if(-1==d.indexOf(i)){let E=d.match(/^\s*at\s+/);if(E){v=E[0]+w+" (http://localhost)";break}}}for(let o=0;o<e.length;o++){const d=e[o];if(d!==t[o])break;s[d]=!0}}()}class He{static get(){return Zone.current.get("ProxyZoneSpec")}static isLoaded(){return He.get()instanceof He}static assertPresent(){if(!He.isLoaded())throw new Error("Expected to be running in 'ProxyZone', but it was not found.");return He.get()}constructor(n=null){this.defaultSpecDelegate=n,this.name="ProxyZone",this._delegateSpec=null,this.properties={ProxyZoneSpec:this},this.propertyKeys=null,this.lastTaskState=null,this.isNeedToTriggerHasTask=!1,this.tasks=[],this.setDelegate(n)}setDelegate(n){const s=this._delegateSpec!==n;this._delegateSpec=n,this.propertyKeys&&this.propertyKeys.forEach(u=>delete this.properties[u]),this.propertyKeys=null,n&&n.properties&&(this.propertyKeys=Object.keys(n.properties),this.propertyKeys.forEach(u=>this.properties[u]=n.properties[u])),s&&this.lastTaskState&&(this.lastTaskState.macroTask||this.lastTaskState.microTask)&&(this.isNeedToTriggerHasTask=!0)}getDelegate(){return this._delegateSpec}resetDelegate(){this.getDelegate(),this.setDelegate(this.defaultSpecDelegate)}tryTriggerHasTask(n,s,u){this.isNeedToTriggerHasTask&&this.lastTaskState&&(this.isNeedToTriggerHasTask=!1,this.onHasTask(n,s,u,this.lastTaskState))}removeFromTasks(n){if(this.tasks)for(let s=0;s<this.tasks.length;s++)if(this.tasks[s]===n)return void this.tasks.splice(s,1)}getAndClearPendingTasksInfo(){if(0===this.tasks.length)return"";const s="--Pending async tasks are: ["+this.tasks.map(u=>{const i=u.data&&Object.keys(u.data).map(w=>w+":"+u.data[w]).join(",");return`type: ${u.type}, source: ${u.source}, args: {${i}}`})+"]";return this.tasks=[],s}onFork(n,s,u,i){return this._delegateSpec&&this._delegateSpec.onFork?this._delegateSpec.onFork(n,s,u,i):n.fork(u,i)}onIntercept(n,s,u,i,w){return this._delegateSpec&&this._delegateSpec.onIntercept?this._delegateSpec.onIntercept(n,s,u,i,w):n.intercept(u,i,w)}onInvoke(n,s,u,i,w,v,R){return this.tryTriggerHasTask(n,s,u),this._delegateSpec&&this._delegateSpec.onInvoke?this._delegateSpec.onInvoke(n,s,u,i,w,v,R):n.invoke(u,i,w,v,R)}onHandleError(n,s,u,i){return this._delegateSpec&&this._delegateSpec.onHandleError?this._delegateSpec.onHandleError(n,s,u,i):n.handleError(u,i)}onScheduleTask(n,s,u,i){return"eventTask"!==i.type&&this.tasks.push(i),this._delegateSpec&&this._delegateSpec.onScheduleTask?this._delegateSpec.onScheduleTask(n,s,u,i):n.scheduleTask(u,i)}onInvokeTask(n,s,u,i,w,v){return"eventTask"!==i.type&&this.removeFromTasks(i),this.tryTriggerHasTask(n,s,u),this._delegateSpec&&this._delegateSpec.onInvokeTask?this._delegateSpec.onInvokeTask(n,s,u,i,w,v):n.invokeTask(u,i,w,v)}onCancelTask(n,s,u,i){return"eventTask"!==i.type&&this.removeFromTasks(i),this.tryTriggerHasTask(n,s,u),this._delegateSpec&&this._delegateSpec.onCancelTask?this._delegateSpec.onCancelTask(n,s,u,i):n.cancelTask(u,i)}onHasTask(n,s,u,i){this.lastTaskState=i,this._delegateSpec&&this._delegateSpec.onHasTask?this._delegateSpec.onHasTask(n,s,u,i):n.hasTask(u,i)}}!function ct(C){yt(C),function it(C){C.ProxyZoneSpec=He}(C),function ot(C){C.SyncTestZoneSpec=class n{constructor(u){this.runZone=C.current,this.name="syncTestZone for "+u}onScheduleTask(u,i,w,v){switch(v.type){case"microTask":case"macroTask":throw new Error(`Cannot call ${v.source} from within a sync test (${this.name}).`);case"eventTask":v=u.scheduleTask(w,v)}return v}}}(C),function Me(C){C.__load_patch("jasmine",(n,s,u)=>{if(!s)throw new Error("Missing: zone.js");if(typeof jest<"u"||typeof jasmine>"u"||jasmine.__zone_patch__)return;jasmine.__zone_patch__=!0;const w=s.SyncTestZoneSpec,v=s.ProxyZoneSpec;if(!w)throw new Error("Missing: SyncTestZoneSpec");if(!v)throw new Error("Missing: ProxyZoneSpec");const R=s.current,S=s.__symbol__,re=!0===n[S("fakeAsyncDisablePatchingClock")],ie=!re&&(!0===n[S("fakeAsyncPatchLock")]||!0===n[S("fakeAsyncAutoFakeAsyncWhenClockPatched")]);if(!0!==n[S("ignoreUnhandledRejection")]){const k=jasmine.GlobalErrors;k&&!jasmine[S("GlobalErrors")]&&(jasmine[S("GlobalErrors")]=k,jasmine.GlobalErrors=function(){const P=new k,p=P.install;return p&&!P[S("install")]&&(P[S("install")]=p,P.install=function(){const e=typeof process<"u"&&!!process.on,t=e?process.listeners("unhandledRejection"):n.eventListeners("unhandledrejection"),o=p.apply(this,arguments);return e?process.removeAllListeners("unhandledRejection"):n.removeAllListeners("unhandledrejection"),t&&t.forEach(d=>{e?process.on("unhandledRejection",d):n.addEventListener("unhandledrejection",d)}),o}),P})}const ce=jasmine.getEnv();if(["describe","xdescribe","fdescribe"].forEach(k=>{let P=ce[k];ce[k]=function(p,e){return P.call(this,p,function ve(k,P){return function(){return R.fork(new w(`jasmine.describe#${k}`)).run(P,this,arguments)}}(p,e))}}),["it","xit","fit"].forEach(k=>{let P=ce[k];ce[S(k)]=P,ce[k]=function(p,e,t){return arguments[1]=I(e),P.apply(this,arguments)}}),["beforeEach","afterEach","beforeAll","afterAll"].forEach(k=>{let P=ce[k];ce[S(k)]=P,ce[k]=function(p,e){return arguments[0]=I(p),P.apply(this,arguments)}}),!re){const k=jasmine[S("clock")]=jasmine.clock;jasmine.clock=function(){const P=k.apply(this,arguments);if(!P[S("patched")]){P[S("patched")]=S("patched");const p=P[S("tick")]=P.tick;P.tick=function(){const t=s.current.get("FakeAsyncTestZoneSpec");return t?t.tick.apply(t,arguments):p.apply(this,arguments)};const e=P[S("mockDate")]=P.mockDate;P.mockDate=function(){const t=s.current.get("FakeAsyncTestZoneSpec");if(t){const o=arguments.length>0?arguments[0]:new Date;return t.setFakeBaseSystemTime.apply(t,o&&"function"==typeof o.getTime?[o.getTime()]:arguments)}return e.apply(this,arguments)},ie&&["install","uninstall"].forEach(t=>{const o=P[S(t)]=P[t];P[t]=function(){if(!s.FakeAsyncTestZoneSpec)return o.apply(this,arguments);jasmine[S("clockInstalled")]="install"===t}})}return P}}if(!jasmine[s.__symbol__("createSpyObj")]){const k=jasmine.createSpyObj;jasmine[s.__symbol__("createSpyObj")]=k,jasmine.createSpyObj=function(){const P=Array.prototype.slice.call(arguments);let e;if(P.length>=3&&P[2]){const t=Object.defineProperty;Object.defineProperty=function(o,d,E){return t.call(this,o,d,{...E,configurable:!0,enumerable:!0})};try{e=k.apply(this,P)}finally{Object.defineProperty=t}}else e=k.apply(this,P);return e}}function U(k,P,p,e){const t=!!jasmine[S("clockInstalled")],o=p.testProxyZone;if(t&&ie){const d=s[s.__symbol__("fakeAsyncTest")];d&&"function"==typeof d.fakeAsync&&(k=d.fakeAsync(k))}return e?o.run(k,P,[e]):o.run(k,P)}function I(k){return k&&(k.length?function(P){return U(k,this,this.queueRunner,P)}:function(){return U(k,this,this.queueRunner)})}const W=jasmine.QueueRunner;jasmine.QueueRunner=function(k){function P(p){p.onComplete&&(p.onComplete=(d=>()=>{this.testProxyZone=null,this.testProxyZoneSpec=null,R.scheduleMicroTask("jasmine.onComplete",d)})(p.onComplete));const e=n[s.__symbol__("setTimeout")],t=n[s.__symbol__("clearTimeout")];e&&(p.timeout={setTimeout:e||n.setTimeout,clearTimeout:t||n.clearTimeout}),jasmine.UserContext?(p.userContext||(p.userContext=new jasmine.UserContext),p.userContext.queueRunner=this):(p.userContext||(p.userContext={}),p.userContext.queueRunner=this);const o=p.onException;p.onException=function(d){if(d&&"Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL."===d.message){const E=this&&this.testProxyZoneSpec;if(E){const D=E.getAndClearPendingTasksInfo();try{d.message+=D}catch{}}}o&&o.call(this,d)},k.call(this,p)}return function(k,P){for(const e in P)P.hasOwnProperty(e)&&(k[e]=P[e]);function p(){this.constructor=k}k.prototype=null===P?Object.create(P):(p.prototype=P.prototype,new p)}(P,k),P.prototype.execute=function(){let p=s.current,e=!1;for(;p;){if(p===R){e=!0;break}p=p.parent}if(!e)throw new Error("Unexpected Zone: "+s.current.name);this.testProxyZoneSpec=new v,this.testProxyZone=R.fork(this.testProxyZoneSpec),s.currentTask?k.prototype.execute.call(this):s.current.scheduleMicroTask("jasmine.execute().forceTask",()=>W.prototype.execute.call(this))},P}(W)})}(C),function Re(C){C.__load_patch("jest",(n,s,u)=>{if(typeof jest>"u"||jest.__zone_patch__)return;s[u.symbol("ignoreConsoleErrorUncaughtError")]=!0,jest.__zone_patch__=!0;const i=s.ProxyZoneSpec,w=s.SyncTestZoneSpec;if(!i)throw new Error("Missing ProxyZoneSpec");const v=s.current,R=v.fork(new w("jest.describe")),S=new i,re=v.fork(S);function ce(U){return function(...I){return R.run(U,this,I)}}function ve(U,I=!1){if("function"!=typeof U)return U;const W=function(){if(!0===s[u.symbol("useFakeTimersCalled")]&&U&&!U.isFakeAsync){const k=s[s.__symbol__("fakeAsyncTest")];k&&"function"==typeof k.fakeAsync&&(U=k.fakeAsync(U))}return S.isTestFunc=I,re.run(U,null,arguments)};return Object.defineProperty(W,"length",{configurable:!0,writable:!0,enumerable:!1}),W.length=U.length,W}["describe","xdescribe","fdescribe"].forEach(U=>{let I=n[U];n[s.__symbol__(U)]||(n[s.__symbol__(U)]=I,n[U]=function(...W){return W[1]=ce(W[1]),I.apply(this,W)},n[U].each=function ie(U){return function(...I){const W=U.apply(this,I);return function(...k){return k[1]=ce(k[1]),W.apply(this,k)}}}(I.each))}),n.describe.only=n.fdescribe,n.describe.skip=n.xdescribe,["it","xit","fit","test","xtest"].forEach(U=>{let I=n[U];n[s.__symbol__(U)]||(n[s.__symbol__(U)]=I,n[U]=function(...W){return W[1]=ve(W[1],!0),I.apply(this,W)},n[U].each=function Se(U){return function(...I){return function(...W){return W[1]=ve(W[1]),U.apply(this,I).apply(this,W)}}}(I.each),n[U].todo=I.todo)}),n.it.only=n.fit,n.it.skip=n.xit,n.test.only=n.fit,n.test.skip=n.xit,["beforeEach","afterEach","beforeAll","afterAll"].forEach(U=>{let I=n[U];n[s.__symbol__(U)]||(n[s.__symbol__(U)]=I,n[U]=function(...W){return W[0]=ve(W[0]),I.apply(this,W)})}),s.patchJestObject=function(I,W=!1){function k(){return!!s.current.get("FakeAsyncTestZoneSpec")}function P(){const p=s.current.get("ProxyZoneSpec");return p&&p.isTestFunc}I[u.symbol("fakeTimers")]||(I[u.symbol("fakeTimers")]=!0,u.patchMethod(I,"_checkFakeTimers",p=>function(e,t){return!!k()||p.apply(e,t)}),u.patchMethod(I,"useFakeTimers",p=>function(e,t){return s[u.symbol("useFakeTimersCalled")]=!0,W||P()?p.apply(e,t):e}),u.patchMethod(I,"useRealTimers",p=>function(e,t){return s[u.symbol("useFakeTimersCalled")]=!1,W||P()?p.apply(e,t):e}),u.patchMethod(I,"setSystemTime",p=>function(e,t){const o=s.current.get("FakeAsyncTestZoneSpec");if(!o||!k())return p.apply(e,t);o.setFakeBaseSystemTime(t[0])}),u.patchMethod(I,"getRealSystemTime",p=>function(e,t){const o=s.current.get("FakeAsyncTestZoneSpec");return o&&k()?o.getRealSystemTime():p.apply(e,t)}),u.patchMethod(I,"runAllTicks",p=>function(e,t){const o=s.current.get("FakeAsyncTestZoneSpec");if(!o)return p.apply(e,t);o.flushMicrotasks()}),u.patchMethod(I,"runAllTimers",p=>function(e,t){const o=s.current.get("FakeAsyncTestZoneSpec");if(!o)return p.apply(e,t);o.flush(100,!0)}),u.patchMethod(I,"advanceTimersByTime",p=>function(e,t){const o=s.current.get("FakeAsyncTestZoneSpec");if(!o)return p.apply(e,t);o.tick(t[0])}),u.patchMethod(I,"runOnlyPendingTimers",p=>function(e,t){const o=s.current.get("FakeAsyncTestZoneSpec");if(!o)return p.apply(e,t);o.flushOnlyPendingTimers()}),u.patchMethod(I,"advanceTimersToNextTimer",p=>function(e,t){const o=s.current.get("FakeAsyncTestZoneSpec");if(!o)return p.apply(e,t);o.tickToNext(t[0])}),u.patchMethod(I,"clearAllTimers",p=>function(e,t){const o=s.current.get("FakeAsyncTestZoneSpec");if(!o)return p.apply(e,t);o.removeAllTimers()}),u.patchMethod(I,"getTimerCount",p=>function(e,t){const o=s.current.get("FakeAsyncTestZoneSpec");return o?o.getTimerCount():p.apply(e,t)}))}})}(C),function ft(C){C.__load_patch("mocha",(n,s)=>{const u=n.Mocha;if(typeof u>"u")return;if(typeof s>"u")throw new Error("Missing Zone.js");const i=s.ProxyZoneSpec,w=s.SyncTestZoneSpec;if(!i)throw new Error("Missing ProxyZoneSpec");if(u.__zone_patch__)throw new Error('"Mocha" has already been patched with "Zone".');u.__zone_patch__=!0;const v=s.current,R=v.fork(new w("Mocha.describe"));let S=null;const re=v.fork(new i),ie={after:n.after,afterEach:n.afterEach,before:n.before,beforeEach:n.beforeEach,describe:n.describe,it:n.it};function Se(I,W,k){for(let P=0;P<I.length;P++){let p=I[P];"function"==typeof p&&(I[P]=0===p.length?W(p):k(p),I[P].toString=function(){return p.toString()})}return I}function ce(I){return Se(I,function(k){return function(){return R.run(k,this,arguments)}})}function ve(I){return Se(I,function(P){return function(){return S.run(P,this)}},function(P){return function(p){return S.run(P,this,[p])}})}function U(I){return Se(I,function(P){return function(){return re.run(P,this)}},function(P){return function(p){return re.run(P,this,[p])}})}var I,W;n.describe=n.suite=function(){return ie.describe.apply(this,ce(arguments))},n.xdescribe=n.suite.skip=n.describe.skip=function(){return ie.describe.skip.apply(this,ce(arguments))},n.describe.only=n.suite.only=function(){return ie.describe.only.apply(this,ce(arguments))},n.it=n.specify=n.test=function(){return ie.it.apply(this,ve(arguments))},n.xit=n.xspecify=n.it.skip=function(){return ie.it.skip.apply(this,ve(arguments))},n.it.only=n.test.only=function(){return ie.it.only.apply(this,ve(arguments))},n.after=n.suiteTeardown=function(){return ie.after.apply(this,U(arguments))},n.afterEach=n.teardown=function(){return ie.afterEach.apply(this,ve(arguments))},n.before=n.suiteSetup=function(){return ie.before.apply(this,U(arguments))},n.beforeEach=n.setup=function(){return ie.beforeEach.apply(this,ve(arguments))},I=u.Runner.prototype.runTest,W=u.Runner.prototype.run,u.Runner.prototype.runTest=function(k){s.current.scheduleMicroTask("mocha.forceTask",()=>{I.call(this,k)})},u.Runner.prototype.run=function(k){return this.on("test",P=>{S=v.fork(new i)}),this.on("fail",(P,p)=>{const e=S&&S.get("ProxyZoneSpec");if(e&&p)try{p.message+=e.getAndClearPendingTasksInfo()}catch{}}),W.call(this,k)}})}(C),function st(C){C.AsyncTestZoneSpec=Ke,C.__load_patch("asynctest",(n,s,u)=>{function i(w,v,R,S){const re=s.current,ie=s.AsyncTestZoneSpec;if(void 0===ie)throw new Error("AsyncTestZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/async-test");const Se=s.ProxyZoneSpec;if(!Se)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");const ce=Se.get();Se.assertPresent();const ve=s.current.getZoneWith("ProxyZoneSpec"),U=ce.getDelegate();return ve.parent.run(()=>{const I=new ie(()=>{ce.getDelegate()==I&&ce.setDelegate(U),I.unPatchPromiseForTest(),re.run(()=>{R()})},W=>{ce.getDelegate()==I&&ce.setDelegate(U),I.unPatchPromiseForTest(),re.run(()=>{S(W)})},"test");ce.setDelegate(I),I.patchPromiseForTest()}),s.current.runGuarded(w,v)}s[u.symbol("asyncTest")]=function(v){return n.jasmine?function(R){R||((R=function(){}).fail=function(S){throw S}),i(v,this,R,S=>{if("string"==typeof S)return R.fail(new Error(S));R.fail(S)})}:function(){return new Promise((R,S)=>{i(v,this,R,S)})}}})}(C),function Qe(C){C.FakeAsyncTestZoneSpec=he,C.__load_patch("fakeasync",(n,s,u)=>{s[u.symbol("fakeAsyncTest")]={resetFakeAsyncZone:Ue,flushMicrotasks:We,discardPeriodicTasks:_t,tick:qe,flush:ge,fakeAsync:se}},!0),xe={setTimeout:le.setTimeout,setInterval:le.setInterval,clearTimeout:le.clearTimeout,clearInterval:le.clearInterval,nativeSetTimeout:le[C.__symbol__("setTimeout")],nativeClearTimeout:le[C.__symbol__("clearTimeout")]},De.nextId=De.getNextId()}(C),function gt(C){C.__load_patch("promisefortest",(n,s,u)=>{const i=u.symbol("state"),v=u.symbol("parentUnresolved");Promise[u.symbol("patchPromiseForTest")]=function(){let S=Promise[s.__symbol__("ZonePromiseThen")];S||(S=Promise[s.__symbol__("ZonePromiseThen")]=Promise.prototype.then,Promise.prototype.then=function(){const re=S.apply(this,arguments);if(null===this[i]){const ie=s.current.get("AsyncTestZoneSpec");ie&&(ie.unresolvedChainedPromiseCount++,re[v]=!0)}return re})},Promise[u.symbol("unPatchPromiseForTest")]=function(){const S=Promise[s.__symbol__("ZonePromiseThen")];S&&(Promise.prototype.then=S,Promise[s.__symbol__("ZonePromiseThen")]=void 0)}})}(C)}(Zone)},96935:()=>{const Me=globalThis;function Re(r){return(Me.__Zone_symbol_prefix||"__zone_symbol__")+r}const Ve=Object.getOwnPropertyDescriptor,rt=Object.defineProperty,Ke=Object.getPrototypeOf,st=Object.create,le=Array.prototype.slice,Fe="addEventListener",Le="removeEventListener",xe=Re(Fe),dt=Re(Le),De="true",he="false",ye=Re("");function et(r,l){return Zone.current.wrap(r,l)}function Ue(r,l,m,c,T){return Zone.current.scheduleMacroTask(r,l,m,c,T)}const se=Re,ze=typeof window<"u",qe=ze?window:void 0,ge=ze&&qe||globalThis,_t="removeAttribute";function We(r,l){for(let m=r.length-1;m>=0;m--)"function"==typeof r[m]&&(r[m]=et(r[m],l+"_"+m));return r}function yt(r){return!r||!1!==r.writable&&!("function"==typeof r.get&&typeof r.set>"u")}const He=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,it=!("nw"in ge)&&typeof ge.process<"u"&&"[object process]"===ge.process.toString(),ot=!it&&!He&&!(!ze||!qe.HTMLElement),gt=typeof ge.process<"u"&&"[object process]"===ge.process.toString()&&!He&&!(!ze||!qe.HTMLElement),ct={},C=se("enable_beforeunload"),n=function(r){if(!(r=r||ge.event))return;let l=ct[r.type];l||(l=ct[r.type]=se("ON_PROPERTY"+r.type));const m=this||r.target||ge,c=m[l];let T;return ot&&m===qe&&"error"===r.type?(T=c&&c.call(this,r.message,r.filename,r.lineno,r.colno,r.error),!0===T&&r.preventDefault()):(T=c&&c.apply(this,arguments),"beforeunload"===r.type&&ge[C]&&"string"==typeof T?r.returnValue=T:null!=T&&!T&&r.preventDefault()),T};function s(r,l,m){let c=Ve(r,l);if(!c&&m&&Ve(m,l)&&(c={enumerable:!0,configurable:!0}),!c||!c.configurable)return;const T=se("on"+l+"patched");if(r.hasOwnProperty(T)&&r[T])return;delete c.writable,delete c.value;const g=c.get,x=c.set,N=l.slice(2);let Z=ct[N];Z||(Z=ct[N]=se("ON_PROPERTY"+N)),c.set=function(Q){let O=this;!O&&r===ge&&(O=ge),O&&("function"==typeof O[Z]&&O.removeEventListener(N,n),x&&x.call(O,null),O[Z]=Q,"function"==typeof Q&&O.addEventListener(N,n,!1))},c.get=function(){let Q=this;if(!Q&&r===ge&&(Q=ge),!Q)return null;const O=Q[Z];if(O)return O;if(g){let B=g.call(this);if(B)return c.set.call(this,B),"function"==typeof Q[_t]&&Q.removeAttribute(l),B}return null},rt(r,l,c),r[T]=!0}function u(r,l,m){if(l)for(let c=0;c<l.length;c++)s(r,"on"+l[c],m);else{const c=[];for(const T in r)"on"==T.slice(0,2)&&c.push(T);for(let T=0;T<c.length;T++)s(r,c[T],m)}}const i=se("originalInstance");function w(r){const l=ge[r];if(!l)return;ge[se(r)]=l,ge[r]=function(){const T=We(arguments,r);switch(T.length){case 0:this[i]=new l;break;case 1:this[i]=new l(T[0]);break;case 2:this[i]=new l(T[0],T[1]);break;case 3:this[i]=new l(T[0],T[1],T[2]);break;case 4:this[i]=new l(T[0],T[1],T[2],T[3]);break;default:throw new Error("Arg list too long.")}},S(ge[r],l);const m=new l(function(){});let c;for(c in m)"XMLHttpRequest"===r&&"responseBlob"===c||function(T){"function"==typeof m[T]?ge[r].prototype[T]=function(){return this[i][T].apply(this[i],arguments)}:rt(ge[r].prototype,T,{set:function(g){"function"==typeof g?(this[i][T]=et(g,r+"."+T),S(this[i][T],g)):this[i][T]=g},get:function(){return this[i][T]}})}(c);for(c in l)"prototype"!==c&&l.hasOwnProperty(c)&&(ge[r][c]=l[c])}function v(r,l,m){let c=r;for(;c&&!c.hasOwnProperty(l);)c=Ke(c);!c&&r[l]&&(c=r);const T=se(l);let g=null;if(c&&(!(g=c[T])||!c.hasOwnProperty(T))&&(g=c[T]=c[l],yt(c&&Ve(c,l)))){const N=m(g,T,l);c[l]=function(){return N(this,arguments)},S(c[l],g)}return g}function R(r,l,m){let c=null;function T(g){const x=g.data;return x.args[x.cbIdx]=function(){g.invoke.apply(this,arguments)},c.apply(x.target,x.args),g}c=v(r,l,g=>function(x,N){const Z=m(x,N);return Z.cbIdx>=0&&"function"==typeof N[Z.cbIdx]?Ue(Z.name,N[Z.cbIdx],Z,T):g.apply(x,N)})}function S(r,l){r[se("OriginalDelegate")]=l}let re=!1,ie=!1;function ce(){if(re)return ie;re=!0;try{const r=qe.navigator.userAgent;(-1!==r.indexOf("MSIE ")||-1!==r.indexOf("Trident/")||-1!==r.indexOf("Edge/"))&&(ie=!0)}catch{}return ie}function ve(r){return"function"==typeof r}function U(r){return"number"==typeof r}let I=!1;if(typeof window<"u")try{const r=Object.defineProperty({},"passive",{get:function(){I=!0}});window.addEventListener("test",r,r),window.removeEventListener("test",r,r)}catch{I=!1}const W={useG:!0},k={},P={},p=new RegExp("^"+ye+"(\\w+)(true|false)$"),e=se("propagationStopped");function t(r,l){const m=(l?l(r):r)+he,c=(l?l(r):r)+De,T=ye+m,g=ye+c;k[r]={},k[r][he]=T,k[r][De]=g}function o(r,l,m,c){const T=c&&c.add||Fe,g=c&&c.rm||Le,x=c&&c.listeners||"eventListeners",N=c&&c.rmAll||"removeAllListeners",Z=se(T),Q="."+T+":",O="prependListener",B="."+O+":",ne=function(H,A,ue){if(H.isRemoved)return;const de=H.callback;let Ae;"object"==typeof de&&de.handleEvent&&(H.callback=j=>de.handleEvent(j),H.originalDelegate=de);try{H.invoke(H,A,[ue])}catch(j){Ae=j}const pe=H.options;return pe&&"object"==typeof pe&&pe.once&&A[g].call(A,ue.type,H.originalDelegate?H.originalDelegate:H.callback,pe),Ae};function fe(H,A,ue){if(!(A=A||r.event))return;const de=H||A.target||r,Ae=de[k[A.type][ue?De:he]];if(Ae){const pe=[];if(1===Ae.length){const j=ne(Ae[0],de,A);j&&pe.push(j)}else{const j=Ae.slice();for(let be=0;be<j.length&&(!A||!0!==A[e]);be++){const J=ne(j[be],de,A);J&&pe.push(J)}}if(1===pe.length)throw pe[0];for(let j=0;j<pe.length;j++){const be=pe[j];l.nativeScheduleMicroTask(()=>{throw be})}}}const Ee=function(H){return fe(this,H,!1)},Ne=function(H){return fe(this,H,!0)};function je(H,A){if(!H)return!1;let ue=!0;A&&void 0!==A.useG&&(ue=A.useG);const de=A&&A.vh;let Ae=!0;A&&void 0!==A.chkDup&&(Ae=A.chkDup);let pe=!1;A&&void 0!==A.rt&&(pe=A.rt);let j=H;for(;j&&!j.hasOwnProperty(T);)j=Ke(j);if(!j&&H[T]&&(j=H),!j||j[Z])return!1;const be=A&&A.eventNameToString,J={},G=j[Z]=j[T],z=j[se(g)]=j[g],X=j[se(x)]=j[x],Ze=j[se(N)]=j[N];let Pe;A&&A.prepend&&(Pe=j[se(A.prepend)]=j[A.prepend]);const we=ue?function(f){if(!J.isExisting)return G.call(J.target,J.eventName,J.capture?Ne:Ee,J.options)}:function(f){return G.call(J.target,J.eventName,f.invoke,J.options)},oe=ue?function(f){if(!f.isRemoved){const y=k[f.eventName];let $;y&&($=y[f.capture?De:he]);const V=$&&f.target[$];if(V)for(let F=0;F<V.length;F++)if(V[F]===f){V.splice(F,1),f.isRemoved=!0,f.removeAbortListener&&(f.removeAbortListener(),f.removeAbortListener=null),0===V.length&&(f.allRemoved=!0,f.target[$]=null);break}}if(f.allRemoved)return z.call(f.target,f.eventName,f.capture?Ne:Ee,f.options)}:function(f){return z.call(f.target,f.eventName,f.invoke,f.options)},tt=A&&A.diff?A.diff:function(f,y){const $=typeof y;return"function"===$&&f.callback===y||"object"===$&&f.originalDelegate===y},nt=Zone[se("UNPATCHED_EVENTS")],Be=r[se("PASSIVE_EVENTS")],_=function(f,y,$,V,F=!1,K=!1){return function(){const ee=this||r;let te=arguments[0];A&&A.transferEventName&&(te=A.transferEventName(te));let me=arguments[1];if(!me)return f.apply(this,arguments);if(it&&"uncaughtException"===te)return f.apply(this,arguments);let _e=!1;if("function"!=typeof me){if(!me.handleEvent)return f.apply(this,arguments);_e=!0}if(de&&!de(f,me,ee,arguments))return;const ht=I&&!!Be&&-1!==Be.indexOf(te),Xe=function b(f){if("object"==typeof f&&null!==f){const y={...f};return f.signal&&(y.signal=f.signal),y}return f}(function Y(f,y){return!I&&"object"==typeof f&&f?!!f.capture:I&&y?"boolean"==typeof f?{capture:f,passive:!0}:f?"object"==typeof f&&!1!==f.passive?{...f,passive:!0}:f:{passive:!0}:f}(arguments[2],ht)),Et=Xe?.signal;if(Et?.aborted)return;if(nt)for(let Je=0;Je<nt.length;Je++)if(te===nt[Je])return ht?f.call(ee,te,me,Xe):f.apply(this,arguments);const At=!!Xe&&("boolean"==typeof Xe||Xe.capture),Dt=!(!Xe||"object"!=typeof Xe)&&Xe.once,jt=Zone.current;let Ct=k[te];Ct||(t(te,be),Ct=k[te]);const Ot=Ct[At?De:he];let vt,bt=ee[Ot],Mt=!1;if(bt){if(Mt=!0,Ae)for(let Je=0;Je<bt.length;Je++)if(tt(bt[Je],me))return}else bt=ee[Ot]=[];const xt=ee.constructor.name,Nt=P[xt];Nt&&(vt=Nt[te]),vt||(vt=xt+y+(be?be(te):te)),J.options=Xe,Dt&&(J.options.once=!1),J.target=ee,J.capture=At,J.eventName=te,J.isExisting=Mt;const St=ue?W:void 0;St&&(St.taskData=J),Et&&(J.options.signal=void 0);const Ge=jt.scheduleEventTask(vt,me,St,$,V);if(Et){J.options.signal=Et;const Je=()=>Ge.zone.cancelTask(Ge);f.call(Et,"abort",Je,{once:!0}),Ge.removeAbortListener=()=>Et.removeEventListener("abort",Je)}return J.target=null,St&&(St.taskData=null),Dt&&(J.options.once=!0),!I&&"boolean"==typeof Ge.options||(Ge.options=Xe),Ge.target=ee,Ge.capture=At,Ge.eventName=te,_e&&(Ge.originalDelegate=me),K?bt.unshift(Ge):bt.push(Ge),F?ee:void 0}};return j[T]=_(G,Q,we,oe,pe),Pe&&(j[O]=_(Pe,B,function(f){return Pe.call(J.target,J.eventName,f.invoke,J.options)},oe,pe,!0)),j[g]=function(){const f=this||r;let y=arguments[0];A&&A.transferEventName&&(y=A.transferEventName(y));const $=arguments[2],V=!!$&&("boolean"==typeof $||$.capture),F=arguments[1];if(!F)return z.apply(this,arguments);if(de&&!de(z,F,f,arguments))return;const K=k[y];let ee;K&&(ee=K[V?De:he]);const te=ee&&f[ee];if(te)for(let me=0;me<te.length;me++){const _e=te[me];if(tt(_e,F))return te.splice(me,1),_e.isRemoved=!0,0!==te.length||(_e.allRemoved=!0,f[ee]=null,V||"string"!=typeof y)||(f[ye+"ON_PROPERTY"+y]=null),_e.zone.cancelTask(_e),pe?f:void 0}return z.apply(this,arguments)},j[x]=function(){const f=this||r;let y=arguments[0];A&&A.transferEventName&&(y=A.transferEventName(y));const $=[],V=d(f,be?be(y):y);for(let F=0;F<V.length;F++){const K=V[F];$.push(K.originalDelegate?K.originalDelegate:K.callback)}return $},j[N]=function(){const f=this||r;let y=arguments[0];if(y){A&&A.transferEventName&&(y=A.transferEventName(y));const $=k[y];if($){const K=f[$[he]],ee=f[$[De]];if(K){const te=K.slice();for(let me=0;me<te.length;me++){const _e=te[me];this[g].call(this,y,_e.originalDelegate?_e.originalDelegate:_e.callback,_e.options)}}if(ee){const te=ee.slice();for(let me=0;me<te.length;me++){const _e=te[me];this[g].call(this,y,_e.originalDelegate?_e.originalDelegate:_e.callback,_e.options)}}}}else{const $=Object.keys(f);for(let V=0;V<$.length;V++){const K=p.exec($[V]);let ee=K&&K[1];ee&&"removeListener"!==ee&&this[N].call(this,ee)}this[N].call(this,"removeListener")}if(pe)return this},S(j[T],G),S(j[g],z),Ze&&S(j[N],Ze),X&&S(j[x],X),!0}let Ie=[];for(let H=0;H<m.length;H++)Ie[H]=je(m[H],c);return Ie}function d(r,l){if(!l){const g=[];for(let x in r){const N=p.exec(x);let Z=N&&N[1];if(Z&&(!l||Z===l)){const Q=r[x];if(Q)for(let O=0;O<Q.length;O++)g.push(Q[O])}}return g}let m=k[l];m||(t(l),m=k[l]);const c=r[m[he]],T=r[m[De]];return c?T?c.concat(T):c.slice():T?T.slice():[]}function E(r,l){const m=r.Event;m&&m.prototype&&l.patchMethod(m.prototype,"stopImmediatePropagation",c=>function(T,g){T[e]=!0,c&&c.apply(T,g)})}const ke=se("zoneTask");function ae(r,l,m,c){let T=null,g=null;m+=c;const x={};function N(Q){const O=Q.data;O.args[0]=function(){return Q.invoke.apply(this,arguments)};const B=T.apply(r,O.args);return U(B)?O.handleId=B:(O.handle=B,O.isRefreshable=ve(B.refresh)),Q}function Z(Q){const{handle:O,handleId:B}=Q.data;return g.call(r,O??B)}T=v(r,l+=c,Q=>function(O,B){if(ve(B[0])){const ne={isRefreshable:!1,isPeriodic:"Interval"===c,delay:"Timeout"===c||"Interval"===c?B[1]||0:void 0,args:B},fe=B[0];B[0]=function(){try{return fe.apply(this,arguments)}finally{const{handle:ue,handleId:de,isPeriodic:Ae,isRefreshable:pe}=ne;!Ae&&!pe&&(de?delete x[de]:ue&&(ue[ke]=null))}};const Ee=Ue(l,B[0],ne,N,Z);if(!Ee)return Ee;const{handleId:Ne,handle:je,isRefreshable:Ie,isPeriodic:H}=Ee.data;if(Ne)x[Ne]=Ee;else if(je&&(je[ke]=Ee,Ie&&!H)){const A=je.refresh;je.refresh=function(){const{zone:ue,state:de}=Ee;return"notScheduled"===de?(Ee._state="scheduled",ue._updateTaskCount(Ee,1)):"running"===de&&(Ee._state="scheduling"),A.call(this)}}return je??Ne??Ee}return Q.apply(r,B)}),g=v(r,m,Q=>function(O,B){const ne=B[0];let fe;U(ne)?(fe=x[ne],delete x[ne]):(fe=ne?.[ke],fe?ne[ke]=null:fe=ne),fe?.type?fe.cancelFn&&fe.zone.cancelTask(fe):Q.apply(r,B)})}function ut(r,l,m){if(!m||0===m.length)return l;const c=m.filter(g=>g.target===r);if(!c||0===c.length)return l;const T=c[0].ignoreProperties;return l.filter(g=>-1===T.indexOf(g))}function Tt(r,l,m,c){r&&u(r,ut(r,l,m),c)}function pt(r){return Object.getOwnPropertyNames(r).filter(l=>l.startsWith("on")&&l.length>2).map(l=>l.substring(2))}function Pt(r,l,m,c,T){const g=Zone.__symbol__(c);if(l[g])return;const x=l[g]=l[c];l[c]=function(N,Z,Q){return Z&&Z.prototype&&T.forEach(function(O){const B=`${m}.${c}::`+O,ne=Z.prototype;try{if(ne.hasOwnProperty(O)){const fe=r.ObjectGetOwnPropertyDescriptor(ne,O);fe&&fe.value?(fe.value=r.wrapWithCurrentZone(fe.value,B),r._redefineProperty(Z.prototype,O,fe)):ne[O]&&(ne[O]=r.wrapWithCurrentZone(ne[O],B))}else ne[O]&&(ne[O]=r.wrapWithCurrentZone(ne[O],B))}catch{}}),x.call(l,N,Z,Q)},r.attachOriginToPatched(l[c],x)}const Rt=function Ye(){const r=globalThis,l=!0===r[Re("forceDuplicateZoneCheck")];if(r.Zone&&(l||"function"!=typeof r.Zone.__symbol__))throw new Error("Zone already loaded.");return r.Zone??=function ft(){const r=Me.performance;function l(Y){r&&r.mark&&r.mark(Y)}function m(Y,M){r&&r.measure&&r.measure(Y,M)}l("Zone");let c=(()=>{class Y{static{this.__symbol__=Re}static assertZonePatched(){if(Me.Promise!==J.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let a=Y.current;for(;a.parent;)a=a.parent;return a}static get current(){return z.zone}static get currentTask(){return X}static __load_patch(a,h,L=!1){if(J.hasOwnProperty(a)){const q=!0===Me[Re("forceDuplicateZoneCheck")];if(!L&&q)throw Error("Already loaded patch: "+a)}else if(!Me["__Zone_disable_"+a]){const q="Zone:"+a;l(q),J[a]=h(Me,Y,G),m(q,q)}}get parent(){return this._parent}get name(){return this._name}constructor(a,h){this._parent=a,this._name=h?h.name||"unnamed":"<root>",this._properties=h&&h.properties||{},this._zoneDelegate=new g(this,this._parent&&this._parent._zoneDelegate,h)}get(a){const h=this.getZoneWith(a);if(h)return h._properties[a]}getZoneWith(a){let h=this;for(;h;){if(h._properties.hasOwnProperty(a))return h;h=h._parent}return null}fork(a){if(!a)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,a)}wrap(a,h){if("function"!=typeof a)throw new Error("Expecting function got: "+a);const L=this._zoneDelegate.intercept(this,a,h),q=this;return function(){return q.runGuarded(L,this,arguments,h)}}run(a,h,L,q){z={parent:z,zone:this};try{return this._zoneDelegate.invoke(this,a,h,L,q)}finally{z=z.parent}}runGuarded(a,h=null,L,q){z={parent:z,zone:this};try{try{return this._zoneDelegate.invoke(this,a,h,L,q)}catch(we){if(this._zoneDelegate.handleError(this,we))throw we}}finally{z=z.parent}}runTask(a,h,L){if(a.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(a.zone||je).name+"; Execution: "+this.name+")");const q=a,{type:we,data:{isPeriodic:oe=!1,isRefreshable:lt=!1}={}}=a;if(a.state===Ie&&(we===be||we===j))return;const tt=a.state!=ue;tt&&q._transitionTo(ue,A);const nt=X;X=q,z={parent:z,zone:this};try{we==j&&a.data&&!oe&&!lt&&(a.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,q,h,L)}catch(Be){if(this._zoneDelegate.handleError(this,Be))throw Be}}finally{const Be=a.state;if(Be!==Ie&&Be!==Ae)if(we==be||oe||lt&&Be===H)tt&&q._transitionTo(A,ue,H);else{const b=q._zoneDelegates;this._updateTaskCount(q,-1),tt&&q._transitionTo(Ie,ue,Ie),lt&&(q._zoneDelegates=b)}z=z.parent,X=nt}}scheduleTask(a){if(a.zone&&a.zone!==this){let L=this;for(;L;){if(L===a.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${a.zone.name}`);L=L.parent}}a._transitionTo(H,Ie);const h=[];a._zoneDelegates=h,a._zone=this;try{a=this._zoneDelegate.scheduleTask(this,a)}catch(L){throw a._transitionTo(Ae,H,Ie),this._zoneDelegate.handleError(this,L),L}return a._zoneDelegates===h&&this._updateTaskCount(a,1),a.state==H&&a._transitionTo(A,H),a}scheduleMicroTask(a,h,L,q){return this.scheduleTask(new x(pe,a,h,L,q,void 0))}scheduleMacroTask(a,h,L,q,we){return this.scheduleTask(new x(j,a,h,L,q,we))}scheduleEventTask(a,h,L,q,we){return this.scheduleTask(new x(be,a,h,L,q,we))}cancelTask(a){if(a.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(a.zone||je).name+"; Execution: "+this.name+")");if(a.state===A||a.state===ue){a._transitionTo(de,A,ue);try{this._zoneDelegate.cancelTask(this,a)}catch(h){throw a._transitionTo(Ae,de),this._zoneDelegate.handleError(this,h),h}return this._updateTaskCount(a,-1),a._transitionTo(Ie,de),a.runCount=-1,a}}_updateTaskCount(a,h){const L=a._zoneDelegates;-1==h&&(a._zoneDelegates=null);for(let q=0;q<L.length;q++)L[q]._updateTaskCount(a.type,h)}}return Y})();const T={name:"",onHasTask:(Y,M,a,h)=>Y.hasTask(a,h),onScheduleTask:(Y,M,a,h)=>Y.scheduleTask(a,h),onInvokeTask:(Y,M,a,h,L,q)=>Y.invokeTask(a,h,L,q),onCancelTask:(Y,M,a,h)=>Y.cancelTask(a,h)};class g{get zone(){return this._zone}constructor(M,a,h){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this._zone=M,this._parentDelegate=a,this._forkZS=h&&(h&&h.onFork?h:a._forkZS),this._forkDlgt=h&&(h.onFork?a:a._forkDlgt),this._forkCurrZone=h&&(h.onFork?this._zone:a._forkCurrZone),this._interceptZS=h&&(h.onIntercept?h:a._interceptZS),this._interceptDlgt=h&&(h.onIntercept?a:a._interceptDlgt),this._interceptCurrZone=h&&(h.onIntercept?this._zone:a._interceptCurrZone),this._invokeZS=h&&(h.onInvoke?h:a._invokeZS),this._invokeDlgt=h&&(h.onInvoke?a:a._invokeDlgt),this._invokeCurrZone=h&&(h.onInvoke?this._zone:a._invokeCurrZone),this._handleErrorZS=h&&(h.onHandleError?h:a._handleErrorZS),this._handleErrorDlgt=h&&(h.onHandleError?a:a._handleErrorDlgt),this._handleErrorCurrZone=h&&(h.onHandleError?this._zone:a._handleErrorCurrZone),this._scheduleTaskZS=h&&(h.onScheduleTask?h:a._scheduleTaskZS),this._scheduleTaskDlgt=h&&(h.onScheduleTask?a:a._scheduleTaskDlgt),this._scheduleTaskCurrZone=h&&(h.onScheduleTask?this._zone:a._scheduleTaskCurrZone),this._invokeTaskZS=h&&(h.onInvokeTask?h:a._invokeTaskZS),this._invokeTaskDlgt=h&&(h.onInvokeTask?a:a._invokeTaskDlgt),this._invokeTaskCurrZone=h&&(h.onInvokeTask?this._zone:a._invokeTaskCurrZone),this._cancelTaskZS=h&&(h.onCancelTask?h:a._cancelTaskZS),this._cancelTaskDlgt=h&&(h.onCancelTask?a:a._cancelTaskDlgt),this._cancelTaskCurrZone=h&&(h.onCancelTask?this._zone:a._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const L=h&&h.onHasTask;(L||a&&a._hasTaskZS)&&(this._hasTaskZS=L?h:T,this._hasTaskDlgt=a,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,h.onScheduleTask||(this._scheduleTaskZS=T,this._scheduleTaskDlgt=a,this._scheduleTaskCurrZone=this._zone),h.onInvokeTask||(this._invokeTaskZS=T,this._invokeTaskDlgt=a,this._invokeTaskCurrZone=this._zone),h.onCancelTask||(this._cancelTaskZS=T,this._cancelTaskDlgt=a,this._cancelTaskCurrZone=this._zone))}fork(M,a){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,M,a):new c(M,a)}intercept(M,a,h){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,M,a,h):a}invoke(M,a,h,L,q){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,M,a,h,L,q):a.apply(h,L)}handleError(M,a){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,M,a)}scheduleTask(M,a){let h=a;if(this._scheduleTaskZS)this._hasTaskZS&&h._zoneDelegates.push(this._hasTaskDlgtOwner),h=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,M,a),h||(h=a);else if(a.scheduleFn)a.scheduleFn(a);else{if(a.type!=pe)throw new Error("Task is missing scheduleFn.");Ee(a)}return h}invokeTask(M,a,h,L){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,M,a,h,L):a.callback.apply(h,L)}cancelTask(M,a){let h;if(this._cancelTaskZS)h=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,M,a);else{if(!a.cancelFn)throw Error("Task is not cancelable");h=a.cancelFn(a)}return h}hasTask(M,a){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,M,a)}catch(h){this.handleError(M,h)}}_updateTaskCount(M,a){const h=this._taskCounts,L=h[M],q=h[M]=L+a;if(q<0)throw new Error("More tasks executed then were scheduled.");0!=L&&0!=q||this.hasTask(this._zone,{microTask:h.microTask>0,macroTask:h.macroTask>0,eventTask:h.eventTask>0,change:M})}}class x{constructor(M,a,h,L,q,we){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=M,this.source=a,this.data=L,this.scheduleFn=q,this.cancelFn=we,!h)throw new Error("callback is not defined");this.callback=h;const oe=this;this.invoke=M===be&&L&&L.useG?x.invokeTask:function(){return x.invokeTask.call(Me,oe,this,arguments)}}static invokeTask(M,a,h){M||(M=this),Ze++;try{return M.runCount++,M.zone.runTask(M,a,h)}finally{1==Ze&&Ne(),Ze--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(Ie,H)}_transitionTo(M,a,h){if(this._state!==a&&this._state!==h)throw new Error(`${this.type} '${this.source}': can not transition to '${M}', expecting state '${a}'${h?" or '"+h+"'":""}, was '${this._state}'.`);this._state=M,M==Ie&&(this._zoneDelegates=null)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const N=Re("setTimeout"),Z=Re("Promise"),Q=Re("then");let ne,O=[],B=!1;function fe(Y){if(ne||Me[Z]&&(ne=Me[Z].resolve(0)),ne){let M=ne[Q];M||(M=ne.then),M.call(ne,Y)}else Me[N](Y,0)}function Ee(Y){0===Ze&&0===O.length&&fe(Ne),Y&&O.push(Y)}function Ne(){if(!B){for(B=!0;O.length;){const Y=O;O=[];for(let M=0;M<Y.length;M++){const a=Y[M];try{a.zone.runTask(a,null,null)}catch(h){G.onUnhandledError(h)}}}G.microtaskDrainDone(),B=!1}}const je={name:"NO ZONE"},Ie="notScheduled",H="scheduling",A="scheduled",ue="running",de="canceling",Ae="unknown",pe="microTask",j="macroTask",be="eventTask",J={},G={symbol:Re,currentZoneFrame:()=>z,onUnhandledError:Pe,microtaskDrainDone:Pe,scheduleMicroTask:Ee,showUncaughtError:()=>!c[Re("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:Pe,patchMethod:()=>Pe,bindArguments:()=>[],patchThen:()=>Pe,patchMacroTask:()=>Pe,patchEventPrototype:()=>Pe,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>Pe,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>Pe,wrapWithCurrentZone:()=>Pe,filterProperties:()=>[],attachOriginToPatched:()=>Pe,_redefineProperty:()=>Pe,patchCallbacks:()=>Pe,nativeScheduleMicroTask:fe};let z={parent:null,zone:new c(null,null)},X=null,Ze=0;function Pe(){}return m("Zone","Zone"),c}(),r.Zone}();(function It(r){(function Ce(r){r.__load_patch("ZoneAwarePromise",(l,m,c)=>{const T=Object.getOwnPropertyDescriptor,g=Object.defineProperty,N=c.symbol,Z=[],Q=!1!==l[N("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],O=N("Promise"),B=N("then"),ne="__creationTrace__";c.onUnhandledError=b=>{if(c.showUncaughtError()){const _=b&&b.rejection;_?console.error("Unhandled Promise rejection:",_ instanceof Error?_.message:_,"; Zone:",b.zone.name,"; Task:",b.task&&b.task.source,"; Value:",_,_ instanceof Error?_.stack:void 0):console.error(b)}},c.microtaskDrainDone=()=>{for(;Z.length;){const b=Z.shift();try{b.zone.runGuarded(()=>{throw b.throwOriginal?b.rejection:b})}catch(_){Ee(_)}}};const fe=N("unhandledPromiseRejectionHandler");function Ee(b){c.onUnhandledError(b);try{const _=m[fe];"function"==typeof _&&_.call(this,b)}catch{}}function Ne(b){return b&&b.then}function je(b){return b}function Ie(b){return oe.reject(b)}const H=N("state"),A=N("value"),ue=N("finally"),de=N("parentPromiseValue"),Ae=N("parentPromiseState"),pe="Promise.then",j=null,be=!0,J=!1,G=0;function z(b,_){return f=>{try{Y(b,_,f)}catch(y){Y(b,!1,y)}}}const X=function(){let b=!1;return function(f){return function(){b||(b=!0,f.apply(null,arguments))}}},Ze="Promise resolved with itself",Pe=N("currentTaskTrace");function Y(b,_,f){const y=X();if(b===f)throw new TypeError(Ze);if(b[H]===j){let $=null;try{("object"==typeof f||"function"==typeof f)&&($=f&&f.then)}catch(V){return y(()=>{Y(b,!1,V)})(),b}if(_!==J&&f instanceof oe&&f.hasOwnProperty(H)&&f.hasOwnProperty(A)&&f[H]!==j)a(f),Y(b,f[H],f[A]);else if(_!==J&&"function"==typeof $)try{$.call(f,y(z(b,_)),y(z(b,!1)))}catch(V){y(()=>{Y(b,!1,V)})()}else{b[H]=_;const V=b[A];if(b[A]=f,b[ue]===ue&&_===be&&(b[H]=b[Ae],b[A]=b[de]),_===J&&f instanceof Error){const F=m.currentTask&&m.currentTask.data&&m.currentTask.data[ne];F&&g(f,Pe,{configurable:!0,enumerable:!1,writable:!0,value:F})}for(let F=0;F<V.length;)h(b,V[F++],V[F++],V[F++],V[F++]);if(0==V.length&&_==J){b[H]=G;let F=f;try{throw new Error("Uncaught (in promise): "+function x(b){return b&&b.toString===Object.prototype.toString?(b.constructor&&b.constructor.name||"")+": "+JSON.stringify(b):b?b.toString():Object.prototype.toString.call(b)}(f)+(f&&f.stack?"\n"+f.stack:""))}catch(K){F=K}Q&&(F.throwOriginal=!0),F.rejection=f,F.promise=b,F.zone=m.current,F.task=m.currentTask,Z.push(F),c.scheduleMicroTask()}}}return b}const M=N("rejectionHandledHandler");function a(b){if(b[H]===G){try{const _=m[M];_&&"function"==typeof _&&_.call(this,{rejection:b[A],promise:b})}catch{}b[H]=J;for(let _=0;_<Z.length;_++)b===Z[_].promise&&Z.splice(_,1)}}function h(b,_,f,y,$){a(b);const V=b[H],F=V?"function"==typeof y?y:je:"function"==typeof $?$:Ie;_.scheduleMicroTask(pe,()=>{try{const K=b[A],ee=!!f&&ue===f[ue];ee&&(f[de]=K,f[Ae]=V);const te=_.run(F,void 0,ee&&F!==Ie&&F!==je?[]:[K]);Y(f,!0,te)}catch(K){Y(f,!1,K)}},f)}const q=function(){},we=l.AggregateError;class oe{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(_){return _ instanceof oe?_:Y(new this(null),be,_)}static reject(_){return Y(new this(null),J,_)}static withResolvers(){const _={};return _.promise=new oe((f,y)=>{_.resolve=f,_.reject=y}),_}static any(_){if(!_||"function"!=typeof _[Symbol.iterator])return Promise.reject(new we([],"All promises were rejected"));const f=[];let y=0;try{for(let F of _)y++,f.push(oe.resolve(F))}catch{return Promise.reject(new we([],"All promises were rejected"))}if(0===y)return Promise.reject(new we([],"All promises were rejected"));let $=!1;const V=[];return new oe((F,K)=>{for(let ee=0;ee<f.length;ee++)f[ee].then(te=>{$||($=!0,F(te))},te=>{V.push(te),y--,0===y&&($=!0,K(new we(V,"All promises were rejected")))})})}static race(_){let f,y,$=new this((K,ee)=>{f=K,y=ee});function V(K){f(K)}function F(K){y(K)}for(let K of _)Ne(K)||(K=this.resolve(K)),K.then(V,F);return $}static all(_){return oe.allWithCallback(_)}static allSettled(_){return(this&&this.prototype instanceof oe?this:oe).allWithCallback(_,{thenCallback:y=>({status:"fulfilled",value:y}),errorCallback:y=>({status:"rejected",reason:y})})}static allWithCallback(_,f){let y,$,V=new this((te,me)=>{y=te,$=me}),F=2,K=0;const ee=[];for(let te of _){Ne(te)||(te=this.resolve(te));const me=K;try{te.then(_e=>{ee[me]=f?f.thenCallback(_e):_e,F--,0===F&&y(ee)},_e=>{f?(ee[me]=f.errorCallback(_e),F--,0===F&&y(ee)):$(_e)})}catch(_e){$(_e)}F++,K++}return F-=2,0===F&&y(ee),V}constructor(_){const f=this;if(!(f instanceof oe))throw new Error("Must be an instanceof Promise.");f[H]=j,f[A]=[];try{const y=X();_&&_(y(z(f,be)),y(z(f,J)))}catch(y){Y(f,!1,y)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return oe}then(_,f){let y=this.constructor?.[Symbol.species];(!y||"function"!=typeof y)&&(y=this.constructor||oe);const $=new y(q),V=m.current;return this[H]==j?this[A].push(V,$,_,f):h(this,V,$,_,f),$}catch(_){return this.then(null,_)}finally(_){let f=this.constructor?.[Symbol.species];(!f||"function"!=typeof f)&&(f=oe);const y=new f(q);y[ue]=ue;const $=m.current;return this[H]==j?this[A].push($,y,_,_):h(this,$,y,_,_),y}}oe.resolve=oe.resolve,oe.reject=oe.reject,oe.race=oe.race,oe.all=oe.all;const lt=l[O]=l.Promise;l.Promise=oe;const tt=N("thenPatched");function nt(b){const _=b.prototype,f=T(_,"then");if(f&&(!1===f.writable||!f.configurable))return;const y=_.then;_[B]=y,b.prototype.then=function($,V){return new oe((K,ee)=>{y.call(this,K,ee)}).then($,V)},b[tt]=!0}return c.patchThen=nt,lt&&(nt(lt),v(l,"fetch",b=>function Be(b){return function(_,f){let y=b.apply(_,f);if(y instanceof oe)return y;let $=y.constructor;return $[tt]||nt($),y}}(b))),Promise[m.__symbol__("uncaughtPromiseErrors")]=Z,oe})})(r),function mt(r){r.__load_patch("toString",l=>{const m=Function.prototype.toString,c=se("OriginalDelegate"),T=se("Promise"),g=se("Error"),x=function(){if("function"==typeof this){const O=this[c];if(O)return"function"==typeof O?m.call(O):Object.prototype.toString.call(O);if(this===Promise){const B=l[T];if(B)return m.call(B)}if(this===Error){const B=l[g];if(B)return m.call(B)}}return m.call(this)};x[c]=m,Function.prototype.toString=x;const N=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":N.call(this)}})}(r),function wt(r){r.__load_patch("util",(l,m,c)=>{const T=pt(l);c.patchOnProperties=u,c.patchMethod=v,c.bindArguments=We,c.patchMacroTask=R;const g=m.__symbol__("BLACK_LISTED_EVENTS"),x=m.__symbol__("UNPATCHED_EVENTS");l[x]&&(l[g]=l[x]),l[g]&&(m[g]=m[x]=l[g]),c.patchEventPrototype=E,c.patchEventTarget=o,c.isIEOrEdge=ce,c.ObjectDefineProperty=rt,c.ObjectGetOwnPropertyDescriptor=Ve,c.ObjectCreate=st,c.ArraySlice=le,c.patchClass=w,c.wrapWithCurrentZone=et,c.filterProperties=ut,c.attachOriginToPatched=S,c._redefineProperty=Object.defineProperty,c.patchCallbacks=Pt,c.getGlobalObjects=()=>({globalSources:P,zoneSymbolEventNames:k,eventNames:T,isBrowser:ot,isMix:gt,isNode:it,TRUE_STR:De,FALSE_STR:he,ZONE_SYMBOL_PREFIX:ye,ADD_EVENT_LISTENER_STR:Fe,REMOVE_EVENT_LISTENER_STR:Le})})}(r)})(Rt),function kt(r){r.__load_patch("legacy",l=>{const m=l[r.__symbol__("legacyPatch")];m&&m()}),r.__load_patch("timers",l=>{const m="set",c="clear";ae(l,m,c,"Timeout"),ae(l,m,c,"Interval"),ae(l,m,c,"Immediate")}),r.__load_patch("requestAnimationFrame",l=>{ae(l,"request","cancel","AnimationFrame"),ae(l,"mozRequest","mozCancel","AnimationFrame"),ae(l,"webkitRequest","webkitCancel","AnimationFrame")}),r.__load_patch("blocking",(l,m)=>{const c=["alert","prompt","confirm"];for(let T=0;T<c.length;T++)v(l,c[T],(x,N,Z)=>function(Q,O){return m.current.run(x,l,O,Z)})}),r.__load_patch("EventTarget",(l,m,c)=>{(function at(r,l){l.patchEventPrototype(r,l)})(l,c),function Oe(r,l){if(Zone[l.symbol("patchEventTarget")])return;const{eventNames:m,zoneSymbolEventNames:c,TRUE_STR:T,FALSE_STR:g,ZONE_SYMBOL_PREFIX:x}=l.getGlobalObjects();for(let Z=0;Z<m.length;Z++){const Q=m[Z],ne=x+(Q+g),fe=x+(Q+T);c[Q]={},c[Q][g]=ne,c[Q][T]=fe}const N=r.EventTarget;N&&N.prototype&&l.patchEventTarget(r,l,[N&&N.prototype])}(l,c);const T=l.XMLHttpRequestEventTarget;T&&T.prototype&&c.patchEventTarget(l,c,[T.prototype])}),r.__load_patch("MutationObserver",(l,m,c)=>{w("MutationObserver"),w("WebKitMutationObserver")}),r.__load_patch("IntersectionObserver",(l,m,c)=>{w("IntersectionObserver")}),r.__load_patch("FileReader",(l,m,c)=>{w("FileReader")}),r.__load_patch("on_property",(l,m,c)=>{!function $e(r,l){if(it&&!gt||Zone[r.symbol("patchEvents")])return;const m=l.__Zone_ignore_on_properties;let c=[];if(ot){const T=window;c=c.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);const g=function Se(){try{const r=qe.navigator.userAgent;if(-1!==r.indexOf("MSIE ")||-1!==r.indexOf("Trident/"))return!0}catch{}return!1}()?[{target:T,ignoreProperties:["error"]}]:[];Tt(T,pt(T),m&&m.concat(g),Ke(T))}c=c.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let T=0;T<c.length;T++){const g=l[c[T]];g&&g.prototype&&Tt(g.prototype,pt(g.prototype),m)}}(c,l)}),r.__load_patch("customElements",(l,m,c)=>{!function Te(r,l){const{isBrowser:m,isMix:c}=l.getGlobalObjects();(m||c)&&r.customElements&&"customElements"in r&&l.patchCallbacks(l,r.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback","formAssociatedCallback","formDisabledCallback","formResetCallback","formStateRestoreCallback"])}(l,c)}),r.__load_patch("XHR",(l,m)=>{!function Q(O){const B=O.XMLHttpRequest;if(!B)return;const ne=B.prototype;let Ee=ne[xe],Ne=ne[dt];if(!Ee){const G=O.XMLHttpRequestEventTarget;if(G){const z=G.prototype;Ee=z[xe],Ne=z[dt]}}const je="readystatechange",Ie="scheduled";function H(G){const z=G.data,X=z.target;X[x]=!1,X[Z]=!1;const Ze=X[g];Ee||(Ee=X[xe],Ne=X[dt]),Ze&&Ne.call(X,je,Ze);const Pe=X[g]=()=>{if(X.readyState===X.DONE)if(!z.aborted&&X[x]&&G.state===Ie){const M=X[m.__symbol__("loadfalse")];if(0!==X.status&&M&&M.length>0){const a=G.invoke;G.invoke=function(){const h=X[m.__symbol__("loadfalse")];for(let L=0;L<h.length;L++)h[L]===G&&h.splice(L,1);!z.aborted&&G.state===Ie&&a.call(G)},M.push(G)}else G.invoke()}else!z.aborted&&!1===X[x]&&(X[Z]=!0)};return Ee.call(X,je,Pe),X[c]||(X[c]=G),be.apply(X,z.args),X[x]=!0,G}function A(){}function ue(G){const z=G.data;return z.aborted=!0,J.apply(z.target,z.args)}const de=v(ne,"open",()=>function(G,z){return G[T]=0==z[2],G[N]=z[1],de.apply(G,z)}),pe=se("fetchTaskAborting"),j=se("fetchTaskScheduling"),be=v(ne,"send",()=>function(G,z){if(!0===m.current[j]||G[T])return be.apply(G,z);{const X={target:G,url:G[N],isPeriodic:!1,args:z,aborted:!1},Ze=Ue("XMLHttpRequest.send",A,X,H,ue);G&&!0===G[Z]&&!X.aborted&&Ze.state===Ie&&Ze.invoke()}}),J=v(ne,"abort",()=>function(G,z){const X=function fe(G){return G[c]}(G);if(X&&"string"==typeof X.type){if(null==X.cancelFn||X.data&&X.data.aborted)return;X.zone.cancelTask(X)}else if(!0===m.current[pe])return J.apply(G,z)})}(l);const c=se("xhrTask"),T=se("xhrSync"),g=se("xhrListener"),x=se("xhrScheduled"),N=se("xhrURL"),Z=se("xhrErrorBeforeScheduled")}),r.__load_patch("geolocation",l=>{l.navigator&&l.navigator.geolocation&&function Qe(r,l){const m=r.constructor.name;for(let c=0;c<l.length;c++){const T=l[c],g=r[T];if(g){if(!yt(Ve(r,T)))continue;r[T]=(N=>{const Z=function(){return N.apply(this,We(arguments,m+"."+T))};return S(Z,N),Z})(g)}}}(l.navigator.geolocation,["getCurrentPosition","watchPosition"])}),r.__load_patch("PromiseRejectionEvent",(l,m)=>{function c(T){return function(g){d(l,T).forEach(N=>{const Z=l.PromiseRejectionEvent;if(Z){const Q=new Z(T,{promise:g.promise,reason:g.rejection});N.invoke(Q)}})}}l.PromiseRejectionEvent&&(m[se("unhandledPromiseRejectionHandler")]=c("unhandledrejection"),m[se("rejectionHandledHandler")]=c("rejectionhandled"))}),r.__load_patch("queueMicrotask",(l,m,c)=>{!function D(r,l){l.patchMethod(r,"queueMicrotask",m=>function(c,T){Zone.current.scheduleMicroTask("queueMicrotask",T[0])})}(l,c)})}(Rt)}},Me=>{Me(Me.s=12523)}]);