# app.py
from flask import Flask, render_template, request, send_file, jsonify, make_response
from google.oauth2 import service_account
from google.analytics.data_v1beta import BetaAnalyticsDataClient
from google.analytics.data_v1beta.types import RunReportRequest, DateRange, Metric, Dimension
from googleapiclient.discovery import build
import pandas as pd
import os
from datetime import datetime, timedelta
import imgkit
from io import BytesIO
import pdfkit
import tempfile

app = Flask(__name__)

# Configuration
KEY_PATH = "config_file.json"
PROPERTY_ID = os.getenv("PROPERTY_ID")
DEFAULT_START_DATE = "2024-03-01"
DEFAULT_END_DATE = "2024-03-31"

def fetch_ga4_data(start_date, end_date):
    # Authenticate with GA4
    credentials = service_account.Credentials.from_service_account_file(KEY_PATH)
    ga_client = BetaAnalyticsDataClient(credentials=credentials)
    
    # Fetch summary metrics
    summary_request = RunReportRequest(
        property=f"properties/{PROPERTY_ID}",
        metrics=[
            Metric(name="totalUsers"),
            Metric(name="newUsers"),
            Metric(name="sessions"),
            Metric(name="engagedSessions"),
            Metric(name="engagementRate"),
            Metric(name="averageSessionDuration"),
            Metric(name="eventCount"),
            Metric(name="screenPageViews"),
            Metric(name="sessionsPerUser"),
        ],
        date_ranges=[DateRange(start_date=start_date, end_date=end_date)]
    )
    
    summary_response = ga_client.run_report(request=summary_request)
    summary_data = {}
    for row in summary_response.rows:
        for i, metric in enumerate(summary_response.metric_headers):
            metric_name = metric.name
            value = row.metric_values[i].value
            try:
                value = float(value)
            except ValueError:
                pass
            summary_data[metric_name] = value
    
    # Fetch top cities
    city_request = RunReportRequest(
        property=f"properties/{PROPERTY_ID}",
        dimensions=[Dimension(name="city")],
        metrics=[Metric(name="sessions")],
        date_ranges=[DateRange(start_date=start_date, end_date=end_date)],
        order_bys=[{"metric": {"metric_name": "sessions"}, "desc": True}],
        limit=10
    )
    
    city_response = ga_client.run_report(request=city_request)
    top_cities = []
    for row in city_response.rows:
        city = row.dimension_values[0].value
        sessions = int(row.metric_values[0].value)
        top_cities.append({"city": city, "sessions": sessions})
    
    # Fetch landing pages
    page_request = RunReportRequest(
        property=f"properties/{PROPERTY_ID}",
        dimensions=[Dimension(name="pagePath")],
        metrics=[Metric(name="screenPageViews")],
        date_ranges=[DateRange(start_date=start_date, end_date=end_date)],
        order_bys=[{"metric": {"metric_name": "screenPageViews"}, "desc": True}],
        limit=10
    )
    
    page_response = ga_client.run_report(request=page_request)
    landing_pages = []
    for row in page_response.rows:
        page = row.dimension_values[0].value
        views = int(row.metric_values[0].value)
        landing_pages.append({"page": page, "views": views})
    
    # Fetch time series data
    time_series_request = RunReportRequest(
        property=f"properties/{PROPERTY_ID}",
        dimensions=[Dimension(name="date")],
        metrics=[
            Metric(name="sessions"),
            Metric(name="engagedSessions")
        ],
        date_ranges=[DateRange(start_date=start_date, end_date=end_date)]
    )
    
    time_response = ga_client.run_report(request=time_series_request)
    dates = []
    sessions = []
    engaged_sessions = []
    time_series_data = []
    for row in time_response.rows:
        date_str = row.dimension_values[0].value  # '20240617'
        raw_date = datetime.strptime(date_str, "%Y%m%d")  # datetime object
        formatted_date = raw_date.strftime("%b %d")       # 'Jun 17'
        
        sessions_count = int(row.metric_values[0].value)
        engaged_count = int(row.metric_values[1].value)

        time_series_data.append((raw_date, formatted_date, sessions_count, engaged_count))

    time_series_data.sort(key=lambda x: x[0])
    dates = [item[1] for item in time_series_data]               # formatted dates
    sessions = [item[2] for item in time_series_data]
    engaged_sessions = [item[3] for item in time_series_data]
    # Fetch device data
    device_request = RunReportRequest(
        property=f"properties/{PROPERTY_ID}",
        dimensions=[Dimension(name="deviceCategory")],
        metrics=[Metric(name="sessions")],
        date_ranges=[DateRange(start_date=start_date, end_date=end_date)]
    )
    
    device_response = ga_client.run_report(request=device_request)
    device_data = []
    total_device_sessions = 0
    for row in device_response.rows:
        device = row.dimension_values[0].value
        device_sessions = int(row.metric_values[0].value)
        total_device_sessions += device_sessions
        device_data.append({"device": device, "sessions": device_sessions})
    
    # Calculate device percentages
    for device in device_data:
        device["percentage"] = round((device["sessions"] / total_device_sessions) * 100) if total_device_sessions else 0
    
    # Fetch channel data
    channel_request = RunReportRequest(
        property=f"properties/{PROPERTY_ID}",
        dimensions=[Dimension(name="sessionDefaultChannelGroup")],
        metrics=[Metric(name="sessions")],
        date_ranges=[DateRange(start_date=start_date, end_date=end_date)]
    )
    
    channel_response = ga_client.run_report(request=channel_request)
    channel_data = []
    total_channel_sessions = 0
    for row in channel_response.rows:
        channel = row.dimension_values[0].value
        channel_sessions = int(row.metric_values[0].value)
        total_channel_sessions += channel_sessions
        channel_data.append({"channel": channel, "sessions": channel_sessions})
    
    # Calculate channel percentages
    for channel in channel_data:
        channel["percentage"] = round((channel["sessions"] / total_channel_sessions) * 100) if total_channel_sessions else 0
    
    # Fetch search queries
    search_service = build('searchconsole', 'v1', credentials=credentials)
    search_query = search_service.searchanalytics().query(
        siteUrl='https://partners.ycuuk.com/',
        body={
            "startDate": start_date,
            "endDate": end_date,
            "dimensions": ["query"],
            "rowLimit": 10,
            "startRow": 0
        }
    ).execute()
    
    search_queries = []
    for row in search_query.get("rows", []):
        query = row["keys"][0]
        impressions = row.get("impressions", 0)
        search_queries.append({"query": query, "impressions": impressions})
    
    # Prepare metrics for display
    metrics = {
        "impressions": int(summary_data.get("screenPageViews", 0)),
        "clicks": int(summary_data.get("eventCount", 0)),
        "total_users": int(summary_data.get("totalUsers", 0)),
        "new_users": int(summary_data.get("newUsers", 0)),
        "sessions": int(summary_data.get("sessions", 0)),
        "engaged_sessions": int(summary_data.get("engagedSessions", 0)),
        "engagement_rate": round(float(summary_data.get("engagementRate", 0)) * 100, 2),
        "avg_engagement": summary_data.get("averageSessionDuration", "00:00:00"),
        "event_count": int(summary_data.get("eventCount", 0)),
        "events_per_session": round(float(summary_data.get("sessionsPerUser", 0)), 2),
        "pageviews": int(summary_data.get("screenPageViews", 0)),
        "views_per_session": round(float(summary_data.get("screenPageViews", 0)) / float(summary_data.get("sessions", 1)), 2)
    }
    
    # Format numbers with commas for display
    for key in metrics:
        if isinstance(metrics[key], (int, float)) and key != "engagement_rate":
            metrics[key] = "{:,}".format(metrics[key])
    
    # Format engagement rate as percentage
    metrics["engagement_rate"] = f"{metrics['engagement_rate']}%"
    
    return {
        "metrics": metrics,
        "time_series": {
            "dates": dates,
            "sessions": sessions,
            "engaged_sessions": engaged_sessions
        },
        "channel_data": {
            "labels": [c["channel"] for c in channel_data],
            "sessions": [c["sessions"] for c in channel_data],
            "percentages": [c["percentage"] for c in channel_data]
        },
        "device_data": {
            "labels": [d["device"] for d in device_data],
            "sessions": [d["sessions"] for d in device_data],
            "percentages": [d["percentage"] for d in device_data]
        },
        "search_queries": search_queries,
        "top_cities": top_cities,
        "landing_pages": landing_pages,
        "report_date": datetime.now().strftime("%B %d, %Y"),
        "period": f"{datetime.strptime(start_date, '%Y-%m-%d').strftime('%B %d, %Y')} - {datetime.strptime(end_date, '%Y-%m-%d').strftime('%B %d, %Y')}",
        "start_date": start_date,
        "end_date": end_date
    }

@app.route('/')
def seo_report():
    start_date = request.args.get('start_date', DEFAULT_START_DATE)
    end_date = request.args.get('end_date', DEFAULT_END_DATE)
    report_data = fetch_ga4_data(start_date, end_date)
    return render_template('report.html', **report_data)


if __name__ == '__main__':
    app.run(debug=True, port=5000)