<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Snapshot: Monthly Review</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a3a6c, #2c5282);
            color: #333;
            padding: 20px;
            min-height: 100vh;
        }
        
        .report-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1a3a6c, #2c5282);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .report-info {
            background-color: #e3f2fd;
            padding: 15px 30px;
            border-bottom: 1px solid #bbdefb;
            font-size: 0.95rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .report-period {
            font-weight: bold;
            color: #1a3a6c;
            font-size: 1.1rem;
        }
        
        .contact-link {
            color: #1a3a6c;
            text-decoration: none;
            font-weight: 500;
        }
        
        .contact-link i {
            margin-right: 5px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 15px;
            padding: 30px;
            background-color: white;
        }
        
        .metric-card {
            background: linear-gradient(to bottom, #f5f7fa, #e3e7f0);
            border-radius: 10px;
            padding: 20px 15px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.1);
            background: linear-gradient(to bottom, #e3e7f0, #d5dae8);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #1a3a6c;
            margin: 10px 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .metric-label {
            font-size: 0.95rem;
            color: #546e7a;
            font-weight: 500;
        }
        
        .chart-section {
            background-color: #f8f9fa;
            padding: 30px;
        }
        
        .chart-row {
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            background-color: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            flex: 1;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .chart-title {
            font-size: 1.4rem;
            color: #1a3a6c;
            font-weight: 600;
        }
        
        .chart-main {
            height: 300px;
            margin-bottom: 20px;
        }
        
        .chart-data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: white;
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .chart-data-table th {
            background-color: #1a3a6c;
            color: white;
            text-align: left;
            padding: 12px 15px;
            font-weight: 500;
        }
        
        .chart-data-table tr:nth-child(even) {
            background-color: #f5f7fa;
        }
        
        .chart-data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .data-section {
            padding: 30px;
            background-color: white;
        }
        
        .section-title {
            font-size: 1.6rem;
            color: #1a3a6c;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 3px solid #4caf50;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 10px;
            color: #4caf50;
        }
        
        .data-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .data-table th {
            background-color: #1a3a6c;
            color: white;
            text-align: left;
            padding: 15px;
            font-weight: 500;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .data-table td {
            padding: 14px 15px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .footer {
            background: linear-gradient(135deg, #1a3a6c, #2c5282);
            color: white;
            text-align: center;
            padding: 25px;
            font-size: 1rem;
        }
        
        .info-box {
            background-color: #e3f2fd;
            border-left: 5px solid #1a3a6c;
            padding: 20px;
            margin: 20px 30px;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        
        .info-box p {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .info-box p:last-child {
            margin-bottom: 0;
        }
        
        .page-title {
            text-align: center;
            margin: 20px 0 30px;
            color: white;
            font-size: 2.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        @media (max-width: 992px) {
            .metrics-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .chart-row {
                flex-direction: column;
            }
            
            .data-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header h1 {
                font-size: 2.2rem;
            }
        }
    </style>
</head>
<body>
    <h1 class="page-title">SEO Snapshot: Monthly Review</h1>
    
    <div class="report-container">
        <div class="header">
            <h1>SEO Performance Dashboard</h1>
            <p>How is my campaign performing?</p>
        </div>
        
        <div class="report-info">
            <div class="report-period">
                <i class="fas fa-calendar-alt"></i> Report Period: March 1, 2024 - March 31, 2024
            </div>
            <a href="#" class="contact-link">
                <i class="fas fa-headset"></i> Contact Your Digital Presence Manager
            </a>
        </div>
        
        <div class="info-box">
            <p>The metrics in the following report are derived from Google Analytics and Google Search Console. In this snapshot, we may add filters to clean up traffic so we're not over-reporting on organic traffic.</p>
            <p>Throughout your SEO campaign, your DPM may contact you about upcoming work that requires feedback covering things like website content & technical optimizations. Keep in mind that timeliness is key when responding to these notifications because they are critical to the continued performance of your SEO campaign.</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-label">Impressions</div>
                <div class="metric-value">1,396</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Clicks</div>
                <div class="metric-value">34</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Total Users</div>
                <div class="metric-value">9,275</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">New Users</div>
                <div class="metric-value">9,126</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Sessions</div>
                <div class="metric-value">10,274</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Engaged Sessions</div>
                <div class="metric-value">10,028</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Engagement Rate</div>
                <div class="metric-value">97.61%</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Avg. Engagement</div>
                <div class="metric-value">00:00:46</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Event Count</div>
                <div class="metric-value">42,853</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Events/Session</div>
                <div class="metric-value">4.17</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Pageviews</div>
                <div class="metric-value">12,747</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Views/Session</div>
                <div class="metric-value">1.24</div>
            </div>
        </div>
        
        <div class="chart-section">
            <div class="chart-row">
                <div class="chart-container">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-chart-line"></i> Sessions vs Engaged Sessions
                        </div>
                    </div>
                    <div class="chart-main">
                        <canvas id="sessionsChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="chart-row">
                <div class="chart-container">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-chart-pie"></i> Sessions by Channel
                        </div>
                    </div>
                    <div class="chart-main">
                        <canvas id="channelChart"></canvas>
                    </div>
                    <table class="chart-data-table">
                        <thead>
                            <tr>
                                <th>Channel</th>
                                <th>Sessions</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Organic Search</td>
                                <td>5,445</td>
                                <td>53%</td>
                            </tr>
                            <tr>
                                <td>Direct</td>
                                <td>2,055</td>
                                <td>20%</td>
                            </tr>
                            <tr>
                                <td>Referral</td>
                                <td>1,541</td>
                                <td>15%</td>
                            </tr>
                            <tr>
                                <td>Social</td>
                                <td>924</td>
                                <td>9%</td>
                            </tr>
                            <tr>
                                <td>Email</td>
                                <td>308</td>
                                <td>3%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="chart-container">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i class="fas fa-mobile-alt"></i> Sessions by Device
                        </div>
                    </div>
                    <div class="chart-main">
                        <canvas id="deviceChart"></canvas>
                    </div>
                    <table class="chart-data-table">
                        <thead>
                            <tr>
                                <th>Device</th>
                                <th>Sessions</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Desktop</td>
                                <td>6,473</td>
                                <td>63%</td>
                            </tr>
                            <tr>
                                <td>Mobile</td>
                                <td>3,493</td>
                                <td>34%</td>
                            </tr>
                            <tr>
                                <td>Tablet</td>
                                <td>308</td>
                                <td>3%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="data-section">
            <h2 class="section-title">
                <i class="fas fa-search"></i> Top Search Queries
            </h2>
            <div class="data-grid">
                <div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Query</th>
                                <th>Impressions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>lessons with and faster institute</td>
                                <td>72</td>
                            </tr>
                            <tr>
                                <td>lessons with and wasteful center</td>
                                <td>52</td>
                            </tr>
                            <tr>
                                <td>lessons with and wasteful</td>
                                <td>38</td>
                            </tr>
                            <tr>
                                <td>lessons with specialist scottisable</td>
                                <td>33</td>
                            </tr>
                            <tr>
                                <td>lessons with reduction</td>
                                <td>28</td>
                            </tr>
                            <tr>
                                <td>lessons with doctor antenna</td>
                                <td>26</td>
                            </tr>
                            <tr>
                                <td>lessons with & vascular center</td>
                                <td>23</td>
                            </tr>
                            <tr>
                                <td>lessons with reduction scottisable</td>
                                <td>21</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div>
                    <h2 class="section-title" style="margin-top: 0;">
                        <i class="fas fa-city"></i> Top Cities by Views
                    </h2>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>City</th>
                                <th>Views</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Phoenix</td>
                                <td>624</td>
                            </tr>
                            <tr>
                                <td>Los Angeles</td>
                                <td>225</td>
                            </tr>
                            <tr>
                                <td>Ashburn</td>
                                <td>163</td>
                            </tr>
                            <tr>
                                <td>Mutsum</td>
                                <td>161</td>
                            </tr>
                            <tr>
                                <td>Dallas</td>
                                <td>136</td>
                            </tr>
                            <tr>
                                <td>Denver</td>
                                <td>106</td>
                            </tr>
                            <tr>
                                <td>Scottsdale</td>
                                <td>99</td>
                            </tr>
                            <tr>
                                <td>Grindale</td>
                                <td>96</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="data-section">
            <h2 class="section-title">
                <i class="fas fa-file-alt"></i> Top Landing Pages
            </h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Page</th>
                        <th>Views</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>/blog/hair-loss-on-logs-waslon-out-for-preiphenal-artery-disc.</td>
                        <td>7,922</td>
                    </tr>
                    <tr>
                        <td>/vascu.lar/preiphenal-arterial-discsare-pad/</td>
                        <td>572</td>
                    </tr>
                    <tr>
                        <td>/blog/can-massage-therapy-curevaricoose-veins/</td>
                        <td>516</td>
                    </tr>
                    <tr>
                        <td>/blog/how-to-check-for-poor-blood-circulation-as-home/</td>
                        <td>371</td>
                    </tr>
                    <tr>
                        <td>/blog/chronic-venous-imaufficiency-symptoms-and-complie.</td>
                        <td>209</td>
                    </tr>
                    <tr>
                        <td>/blog/what-are-the-proto-and-cone-of-laser-treatment/</td>
                        <td>169</td>
                    </tr>
                    <tr>
                        <td>/scottact-usr/djundala/</td>
                        <td>160</td>
                    </tr>
                    <tr>
                        <td>/scottact-usr/scottisable/</td>
                        <td>150</td>
                    </tr>
                    <tr>
                        <td>/blog/how-to-improve-veh-health-in-logu/</td>
                        <td>146</td>
                    </tr>
                    <tr>
                        <td>/scottact-usr/</td>
                        <td>144</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>For questions regarding this data or your SEO program in general, please contact your Digital Presence Manager (DPM)</p>
            <p>Generated on: June 16, 2025</p>
        </div>
    </div>

    <script>
        // Line Chart: Sessions vs Engaged Sessions
        const sessionsCtx = document.getElementById('sessionsChart').getContext('2d');
        const sessionsChart = new Chart(sessionsCtx, {
            type: 'line',
            data: {
                labels: ['Mar 1', 'Mar 4', 'Mar 7', 'Mar 10', 'Mar 13', 'Mar 16', 'Mar 19', 'Mar 22', 'Mar 25', 'Mar 28', 'Mar 31'],
                datasets: [
                    {
                        label: 'Sessions',
                        data: [120, 190, 150, 210, 180, 200, 170, 230, 190, 220, 250],
                        borderColor: '#1f77b4',
                        backgroundColor: 'rgba(31, 119, 180, 0.1)',
                        borderWidth: 3,
                        tension: 0.3,
                        fill: true,
                        pointBackgroundColor: '#1f77b4',
                        pointRadius: 5
                    },
                    {
                        label: 'Engaged Sessions',
                        data: [100, 170, 130, 190, 160, 180, 150, 210, 170, 200, 230],
                        borderColor: '#ff7f0e',
                        backgroundColor: 'rgba(255, 127, 14, 0.1)',
                        borderWidth: 3,
                        tension: 0.3,
                        fill: true,
                        pointBackgroundColor: '#ff7f0e',
                        pointRadius: 5
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            font: {
                                size: 12
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 12
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            font: {
                                size: 13
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleFont: {
                            size: 14
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 10
                    }
                }
            }
        });

        // Pie Chart: Sessions by Channel
        const channelCtx = document.getElementById('channelChart').getContext('2d');
        const channelChart = new Chart(channelCtx, {
            type: 'doughnut',
            data: {
                labels: ['Organic Search', 'Direct', 'Referral', 'Social', 'Email'],
                datasets: [{
                    data: [53, 20, 15, 9, 3],
                    backgroundColor: [
                        '#1f77b4',
                        '#ff7f0e',
                        '#2ca02c',
                        '#d62728',
                        '#9467bd'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 13
                            },
                            boxWidth: 15,
                            padding: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed}%`;
                            }
                        }
                    }
                },
                cutout: '50%'
            }
        });

        // Pie Chart: Sessions by Device
        const deviceCtx = document.getElementById('deviceChart').getContext('2d');
        const deviceChart = new Chart(deviceCtx, {
            type: 'doughnut',
            data: {
                labels: ['Desktop', 'Mobile', 'Tablet'],
                datasets: [{
                    data: [63, 34, 3],
                    backgroundColor: [
                        '#1f77b4',
                        '#ff7f0e',
                        '#2ca02c'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                size: 13
                            },
                            boxWidth: 15,
                            padding: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed}%`;
                            }
                        }
                    }
                },
                cutout: '50%'
            }
        });
    </script>
</body>
</html>