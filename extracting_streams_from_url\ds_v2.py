import os
import time
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from io import BytesIO
from PIL import Image, ImageTk
import threading
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from pptx import Presentation
from pptx.util import Inches, Pt
import tkinter.messagebox as messagebox
from tkinter import Canvas

class ScreenshotCaptureApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Screenshot Capture Tool")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # Initialize variables
        self.driver = None
        self.wait = None
        self.current_preview_image = None
        self.settings = {
            'scroll_delay': 1.5,
            'first_card_delay': 3.0,
            'wait_timeout': 30,
            'crop_area': None,  # Will store (x, y, width, height)
            'crop_area_relative': False  # Flag to indicate relative coordinates
        }
        
        self.create_widgets()
        
    def create_widgets(self):
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # URL Input Section
        url_frame = ttk.LabelFrame(main_frame, text="URL Configuration", padding="10")
        url_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(url_frame, text="Enter URL:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.url_var = tk.StringVar()
        self.url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=60)
        self.url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        self.url_entry.bind('<Return>', self.open_browser)
        
        self.open_browser_btn = ttk.Button(url_frame, text="Open Browser", command=self.open_browser)
        self.open_browser_btn.grid(row=0, column=2, padx=(5, 0))
        
        url_frame.columnconfigure(1, weight=1)
        
        # Settings Section
        settings_frame = ttk.LabelFrame(main_frame, text="Capture Settings", padding="10")
        settings_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Scroll Delay setting
        ttk.Label(settings_frame, text="Scroll Delay (s):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.scroll_delay_var = tk.DoubleVar(value=self.settings['scroll_delay'])
        scroll_delay_spin = ttk.Spinbox(settings_frame, from_=0.5, to=5.0, increment=0.1, 
                                    textvariable=self.scroll_delay_var, width=10)
        scroll_delay_spin.grid(row=0, column=1, sticky=tk.W, pady=2)
        
        # First Card Delay setting
        ttk.Label(settings_frame, text="First Card Delay (s):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.first_card_delay_var = tk.DoubleVar(value=self.settings['first_card_delay'])
        first_card_delay_spin = ttk.Spinbox(settings_frame, from_=1.0, to=10.0, increment=0.5, 
                                        textvariable=self.first_card_delay_var, width=10)
        first_card_delay_spin.grid(row=1, column=1, sticky=tk.W, pady=2)
        
        # Area Selection
        ttk.Label(settings_frame, text="Crop Area:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.area_info_var = tk.StringVar(value="No area selected")
        area_info_label = ttk.Label(settings_frame, textvariable=self.area_info_var, foreground="blue")
        area_info_label.grid(row=2, column=1, sticky=tk.W, pady=2)
        
        self.select_area_btn = ttk.Button(settings_frame, text="Select Area", 
                                        command=self.select_crop_area, state='disabled')
        self.select_area_btn.grid(row=3, column=0, pady=5)
        
        self.clear_area_btn = ttk.Button(settings_frame, text="Clear Area", 
                                        command=self.clear_crop_area, state='disabled')
        self.clear_area_btn.grid(row=3, column=1, pady=5)
        
        settings_frame.columnconfigure(1, weight=1)
        
        # Preview Section
        preview_frame = ttk.LabelFrame(main_frame, text="Preview", padding="10")
        preview_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0), pady=(0, 10))
        
        # Preview canvas with scrollbar
        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.preview_canvas = tk.Canvas(canvas_frame, width=400, height=300, bg='white')
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.preview_canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.preview_canvas.xview)
        self.preview_canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        self.preview_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        canvas_frame.columnconfigure(0, weight=1)
        canvas_frame.rowconfigure(0, weight=1)
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        
        # Control Buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        self.test_screenshot_btn = ttk.Button(control_frame, text="Test Screenshot", 
                                             command=self.test_screenshot, state='disabled')
        self.test_screenshot_btn.grid(row=0, column=0, padx=5)
        
        self.apply_settings_btn = ttk.Button(control_frame, text="Apply Settings", 
                                           command=self.apply_settings, state='disabled')
        self.apply_settings_btn.grid(row=0, column=1, padx=5)
        
        self.export_all_btn = ttk.Button(control_frame, text="Export All Cards", 
                                        command=self.export_all_cards, state='disabled')
        self.export_all_btn.grid(row=0, column=2, padx=5)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready - Enter URL and open browser to begin")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
    def open_browser(self, event=None):
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter a URL")
            return
            
        try:
            self.status_var.set("Opening browser...")
            self.setup_driver()
            self.driver.get(url)
            self.status_var.set("Browser opened. Navigate to the desired page and come back to test screenshot.")
            
            # Enable buttons
            self.test_screenshot_btn.config(state='normal')
            self.apply_settings_btn.config(state='normal')
            self.export_all_btn.config(state='normal')
            self.select_area_btn.config(state='normal')
            self.clear_area_btn.config(state='normal')
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open browser: {str(e)}")
            self.status_var.set("Error opening browser")
            
    def setup_driver(self):
        if self.driver:
            self.driver.quit()
            
        options = webdriver.ChromeOptions()
        options.add_argument("--window-size=1920,1080")
        options.add_argument("--disable-web-security")
        options.add_argument("--disable-features=VizDisplayCompositor")
        
        self.driver = webdriver.Chrome(options=options)
        self.wait = WebDriverWait(self.driver, self.settings['wait_timeout'])
        self.driver.maximize_window()
        
        # Apply zoom to 80% as in the reference code
        self.driver.execute_script("document.body.style.zoom='80%'")
        time.sleep(0.5)
        
    def apply_settings(self):
        if not self.driver:
            messagebox.showerror("Error", "No browser session active")
            return
            
        try:
            # Update settings
            self.settings['scroll_delay'] = self.scroll_delay_var.get()
            self.settings['first_card_delay'] = self.first_card_delay_var.get()
            
            self.status_var.set("Settings applied")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply settings: {str(e)}")
            
    def select_crop_area(self):
        if not self.driver:
            messagebox.showerror("Error", "No browser session active")
            return
        
        try:
            # Get full page dimensions
            total_width = self.driver.execute_script("return document.body.scrollWidth")
            total_height = self.driver.execute_script("return document.body.scrollHeight")
            viewport_width = self.driver.execute_script("return window.innerWidth")
            viewport_height = self.driver.execute_script("return window.innerHeight")
            
            # Set window size to capture full page
            self.driver.set_window_size(total_width, total_height)
            time.sleep(1)  # Wait for resize
            
            # Take a full screenshot
            screenshot_bytes = self.driver.get_screenshot_as_png()
            full_image = Image.open(BytesIO(screenshot_bytes))
            
            # Restore original window size
            self.driver.set_window_size(viewport_width, viewport_height)
            
            # Create area selection window
            self.create_area_selection_window(full_image)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to capture screenshot for area selection: {str(e)}")

    def create_area_selection_window(self, full_image):
        # Create new window
        area_window = tk.Toplevel(self.root)
        area_window.title("Select Crop Area")
        area_window.geometry("1000x700")
        
        # Scale image to fit window
        display_scale = min(900/full_image.width, 600/full_image.height, 1.0)
        display_width = int(full_image.width * display_scale)
        display_height = int(full_image.height * display_scale)
        display_image = full_image.resize((display_width, display_height), Image.Resampling.LANCZOS)
        
        # Create canvas
        canvas = tk.Canvas(area_window, width=display_width, height=display_height, bg='white')
        canvas.pack(pady=10)
        
        # Display image
        photo = ImageTk.PhotoImage(display_image)
        canvas.create_image(0, 0, anchor=tk.NW, image=photo)
        
        # Variables for selection
        self.selection_start = None
        self.selection_rect = None
        self.selection_coords = None
        
        def on_mouse_down(event):
            self.selection_start = (event.x, event.y)
            if self.selection_rect:
                canvas.delete(self.selection_rect)
        
        def on_mouse_drag(event):
            if self.selection_start:
                if self.selection_rect:
                    canvas.delete(self.selection_rect)
                self.selection_rect = canvas.create_rectangle(
                    self.selection_start[0], self.selection_start[1],
                    event.x, event.y,
                    outline='red', width=2
                )
        
        def on_mouse_up(event):
            if self.selection_start:
                # Calculate selection coordinates (convert back to original image scale)
                x1 = int(min(self.selection_start[0], event.x) / display_scale)
                y1 = int(min(self.selection_start[1], event.y) / display_scale)
                x2 = int(max(self.selection_start[0], event.x) / display_scale)
                y2 = int(max(self.selection_start[1], event.y) / display_scale)
                
                self.selection_coords = (x1, y1, x2 - x1, y2 - y1)  # (x, y, width, height)
        
        def confirm_selection():
            if self.selection_coords:
                # Store both original coordinates and relative coordinates
                self.settings['crop_area'] = self.selection_coords
                self.settings['crop_area_relative'] = True  # Flag to indicate these are relative to full page
                x, y, w, h = self.selection_coords
                self.area_info_var.set(f"Area: {x},{y} ({w}x{h})")
                area_window.destroy()
                
                # Show warning about crop area usage
                messagebox.showinfo("Crop Area Set", 
                                  "Crop area selected. Note: This works best with full page screenshots.\n"
                                  "For element screenshots, cropping may be skipped if coordinates don't match.")
            else:
                messagebox.showwarning("Warning", "Please select an area first")
        
        def cancel_selection():
            area_window.destroy()
        
        # Bind mouse events
        canvas.bind("<Button-1>", on_mouse_down)
        canvas.bind("<B1-Motion>", on_mouse_drag)
        canvas.bind("<ButtonRelease-1>", on_mouse_up)
        
        # Buttons
        button_frame = ttk.Frame(area_window)
        button_frame.pack(pady=10)
        
        ttk.Button(button_frame, text="Confirm Selection", command=confirm_selection).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=cancel_selection).pack(side=tk.LEFT, padx=5)
        
        # Keep reference to photo
        area_window.photo = photo

    def clear_crop_area(self):
        self.settings['crop_area'] = None
        self.settings['crop_area_relative'] = False
        self.area_info_var.set("No area selected")

    def apply_crop_area(self, image):
        if self.settings['crop_area'] is None:
            return image
        
        x, y, width, height = self.settings['crop_area']
        
        # Ensure crop area is within image bounds
        x = max(0, min(x, image.width - 1))
        y = max(0, min(y, image.height - 1))
        width = min(width, image.width - x)
        height = min(height, image.height - y)
        
        # Add minimum size check
        if width > 10 and height > 10:  # Minimum 10x10 pixels
            try:
                cropped = image.crop((x, y, x + width, y + height))
                # Verify the cropped image has content
                if cropped.size[0] > 0 and cropped.size[1] > 0:
                    return cropped
            except Exception as e:
                print(f"Crop failed: {e}")
        
        return image

    def test_screenshot(self):
        if not self.driver:
            messagebox.showerror("Error", "No browser session active")
            return
            
        def capture_test():
            try:
                self.status_var.set("Capturing test screenshot...")
                self.root.update()
                
                # Apply current settings first
                self.apply_settings()
                
                # Get full page dimensions
                total_width = self.driver.execute_script("return document.body.scrollWidth")
                total_height = self.driver.execute_script("return document.body.scrollHeight")
                viewport_width = self.driver.execute_script("return window.innerWidth")
                viewport_height = self.driver.execute_script("return window.innerHeight")
                
                # Set window size to capture full page
                self.driver.set_window_size(total_width, total_height)
                time.sleep(1)  # Wait for resize
                
                # Capture full webpage screenshot
                screenshot_bytes = self.driver.get_screenshot_as_png()
                image = Image.open(BytesIO(screenshot_bytes))
                
                # Restore original window size
                self.driver.set_window_size(viewport_width, viewport_height)
                
                # Apply crop area if selected
                if self.settings['crop_area'] is not None:
                    image = self.apply_crop_area(image)
                
                # Update preview
                self.update_preview(image)
                self.status_var.set("Test screenshot captured successfully")
                
            except Exception as e:
                self.status_var.set(f"Error: {str(e)}")
                messagebox.showerror("Error", str(e))
                
        # Run in separate thread to avoid blocking UI
        threading.Thread(target=capture_test, daemon=True).start()
        
    def update_preview(self, image):
        # Resize image to fit preview canvas
        canvas_width = self.preview_canvas.winfo_width()
        canvas_height = self.preview_canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            canvas_width, canvas_height = 400, 300
            
        # Calculate scaling to fit in canvas
        scale_x = canvas_width / image.width
        scale_y = canvas_height / image.height
        scale = min(scale_x, scale_y, 1.0)  # Don't scale up
        
        if scale < 1.0:
            new_width = int(image.width * scale)
            new_height = int(image.height * scale)
            display_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        else:
            display_image = image
            
        # Convert to PhotoImage
        self.current_preview_image = ImageTk.PhotoImage(display_image)
        
        # Clear canvas and add image
        self.preview_canvas.delete("all")
        self.preview_canvas.create_image(
            canvas_width // 2, canvas_height // 2,
            anchor=tk.CENTER, image=self.current_preview_image
        )
        
        # Update scroll region
        self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))
        
    def export_all_cards(self):
        if not self.driver:
            messagebox.showerror("Error", "No browser session active")
            return
            
        # Ask for save location
        save_path = filedialog.asksaveasfilename(
            defaultextension=".pptx",
            filetypes=[("PowerPoint files", "*.pptx"), ("All files", "*.*")],
            title="Save PowerPoint Presentation"
        )
        
        if not save_path:
            return
            
        def export_process():
            try:
                self.status_var.set("Starting export process...")
                self.root.update()
                
                # Apply current settings
                self.apply_settings()
                
                # Create screenshots directory
                screenshots_dir = "screenshots"
                os.makedirs(screenshots_dir, exist_ok=True)
                
                # Create PowerPoint presentation
                prs = Presentation()
                blank_layout = prs.slide_layouts[6]
                
                # Find all sections
                sections = self.wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".section--info-card")))
                total_sections = len(sections)
                
                self.status_var.set(f"Found {total_sections} sections to process...")
                self.root.update()
                
                for sec_index in range(total_sections):
                    try:
                        sections = self.driver.find_elements(By.CSS_SELECTOR, ".section--info-card")
                        section = sections[sec_index]
                        section_name = section.find_element(By.TAG_NAME, "h5").text.strip()
                        safe_section_name = section_name.replace(" ", "_").replace("/", "-")
                        
                        self.status_var.set(f"Processing section {sec_index+1}/{total_sections}: {section_name}")
                        self.root.update()
                        
                        # Click section
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", section)
                        time.sleep(self.settings['scroll_delay'])
                        self.driver.execute_script("arguments[0].click();", section)
                        time.sleep(self.settings['scroll_delay'] * 2)
                        
                        # Process each tab type
                        for tab_type in ["Cards", "Assessment Questions"]:
                            try:
                                tab_xpath = f"//div[contains(@class, 'mat-tab-label') and contains(text(), '{tab_type}')]"
                                tab = self.wait.until(EC.element_to_be_clickable((By.XPATH, tab_xpath)))
                                self.driver.execute_script("arguments[0].click();", tab)
                                time.sleep(self.settings['scroll_delay'] * 2)
                                
                                cards = self.driver.find_elements(By.CSS_SELECTOR, "app-card div.info-card")
                                card_images = []
                                
                                for card_index in range(len(cards)):
                                    try:
                                        cards = self.driver.find_elements(By.CSS_SELECTOR, "app-card div.info-card")
                                        card = cards[card_index]
                                        card_title = card.text.strip().split("\n")[0].strip()
                                        safe_card_title = "".join(c for c in card_title if c.isalnum() or c in (' ', '_', '-')).strip().replace(" ", "_")
                                        filename = f"{safe_section_name}_{tab_type.replace(' ', '')}_{safe_card_title}.png"
                                        
                                        self.status_var.set(f"Capturing card {card_index+1}/{len(cards)}: {card_title}")
                                        self.root.update()
                                        
                                        # Click card
                                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", card)
                                        time.sleep(self.settings['scroll_delay'])
                                        self.driver.execute_script("arguments[0].click();", card)
                                        time.sleep(self.settings['scroll_delay'])
                                        
                                        # ====== REFERENCE CODE INTEGRATION START ====== #
                                        preview_card = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".preview-card")))
                                        resize_ph = preview_card.find_element(By.CSS_SELECTOR, ".resize-ph")
                                        iframe = resize_ph.find_element(By.CSS_SELECTOR, "iframe")
                                        
                                        # Scroll the preview card into view
                                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", resize_ph)
                                        time.sleep(self.settings['scroll_delay'])
                                        
                                        # First card extra wait
                                        if card_index == 0:
                                            # Switch to iframe and wait for content to load
                                            self.driver.switch_to.frame(iframe)
                                            # Wait until body has some height (content is rendered)
                                            for _ in range(20):
                                                body_height = self.driver.execute_script("return document.body.scrollHeight")
                                                if body_height > 200:  # arbitrary threshold indicating content loaded
                                                    break
                                                time.sleep(0.2)
                                            self.driver.switch_to.default_content()
                                            time.sleep(0.5)  # small buffer
                                        
                                        # Adjust window size to fit the preview card
                                        original_size = self.driver.get_window_size()
                                        element_width = preview_card.size['width']
                                        element_height = preview_card.size['height']
                                        self.driver.set_window_size(element_width + 200, element_height + 300)
                                        time.sleep(self.settings['scroll_delay'])
                                        
                                        # Get the scroll height and client height of the iframe
                                        self.driver.switch_to.frame(iframe)
                                        scroll_height = self.driver.execute_script("return document.body.scrollHeight")
                                        client_height = self.driver.execute_script("return document.documentElement.clientHeight")
                                        self.driver.switch_to.default_content()
                                        
                                        if scroll_height > client_height:
                                            # Scroll and stitch
                                            stitched_images = []
                                            scroll_y = 0
                                            while scroll_y < scroll_height:
                                                self.driver.switch_to.frame(iframe)
                                                self.driver.execute_script(f"window.scrollTo(0, {scroll_y});")
                                                time.sleep(0.8)  # wait for scroll to settle
                                                self.driver.switch_to.default_content()
                                                
                                                # Capture the preview card at this scroll position
                                                image_part = Image.open(BytesIO(preview_card.screenshot_as_png))
                                                stitched_images.append(image_part)
                                                scroll_y += client_height
                                            
                                            # Stitch the images
                                            total_height = sum(img.height for img in stitched_images)
                                            stitched = Image.new('RGB', (stitched_images[0].width, total_height))
                                            offset = 0
                                            for img in stitched_images:
                                                stitched.paste(img, (0, offset))
                                                offset += img.height
                                            image = stitched
                                        else:
                                            # Single capture
                                            image = Image.open(BytesIO(preview_card.screenshot_as_png))
                                        
                                        # Reset window size
                                        self.driver.set_window_size(original_size['width'], original_size['height'])
                                        
                                        # Save image
                                        image_path = os.path.join(screenshots_dir, filename)
                                        image.save(image_path)
                                        card_images.append((card_title, filename, image_path))
                                        # ====== REFERENCE CODE INTEGRATION END ====== #
                                        
                                    except Exception as e:
                                        print(f"Failed to capture card {card_index+1}: {e}")
                                        continue
                                
                                # === Add to PPT Slide: 2 cards per slide ===
                                for i in range(0, len(card_images), 2):
                                    slide = prs.slides.add_slide(blank_layout)
                                    
                                    # Add section title
                                    left = Inches(0.5)
                                    top = Inches(0.2)
                                    width = Inches(9)
                                    height = Inches(0.5)
                                    title_box = slide.shapes.add_textbox(left, top, width, height)
                                    tf = title_box.text_frame
                                    p = tf.paragraphs[0]
                                    p.text = f"Section {sec_index+1} : {section_name}"
                                    p.font.bold = True
                                    p.font.size = Pt(20)
                                    
                                    for j in range(2):
                                        if i + j >= len(card_images):
                                            break
                                        card_title, img_file, img_path = card_images[i + j]
                                        try:
                                            img = Image.open(img_path)
                                            img_w, img_h = img.size
                                            img_ratio = img_w / img_h
                                            
                                            max_h = Inches(5.8)
                                            new_h = max_h
                                            new_w = new_h * img_ratio
                                            
                                            pic_left = Inches(0.5) + j * (Inches(4.5))
                                            pic_top = Inches(0.8)
                                            slide.shapes.add_picture(img_path, pic_left, pic_top, height=new_h)
                                            
                                            # Add label below
                                            label_box = slide.shapes.add_textbox(pic_left, pic_top + new_h + Inches(0.1), Inches(4), Inches(0.4))
                                            label_tf = label_box.text_frame
                                            label_p = label_tf.paragraphs[0]
                                            label_p.text = f"Card {i + j + 1}"
                                            label_p.font.bold = True
                                            label_p.font.size = Pt(16)
                                        except Exception as e:
                                            print(f"Failed to add card to slide: {e}")
                                            continue
                                
                            except Exception as e:
                                print(f"Failed to process tab {tab_type}: {e}")
                                continue
                        
                    except Exception as e:
                        print(f"Failed to process section {sec_index+1}: {e}")
                        continue
                
                # Save PowerPoint
                self.status_var.set("Saving PowerPoint presentation...")
                self.root.update()
                prs.save(save_path)
                
                self.status_var.set(f"Export completed successfully! Saved to: {save_path}")
                messagebox.showinfo("Success", f"Export completed successfully!\nSaved to: {save_path}")
                
            except Exception as e:
                self.status_var.set(f"Export failed: {str(e)}")
                messagebox.showerror("Error", f"Export failed: {str(e)}")
        
        # Run export in separate thread
        threading.Thread(target=export_process, daemon=True).start()
    
    def on_closing(self):
        if self.driver:
            self.driver.quit()
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = ScreenshotCaptureApp(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()