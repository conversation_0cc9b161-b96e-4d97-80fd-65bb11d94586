"""
Test script to verify the simplified full page + crop area capture works correctly.

This script tests the updated deepseek_v1.py to ensure:
1. Only full page screenshot with crop area is used
2. No smart preview or element targeting methods
3. Consistent results across different display dimensions
4. User-defined crop area is properly applied
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """Test that the updated script imports correctly"""
    try:
        from deepseek_v1 import ScreenshotCaptureApp
        print("✓ Successfully imported ScreenshotCaptureApp")
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_settings():
    """Test that settings are properly configured"""
    try:
        from deepseek_v1 import ScreenshotCaptureApp
        import tkinter as tk
        
        root = tk.Tk()
        app = ScreenshotCaptureApp(root)
        
        # Check that smart preview settings are removed
        expected_settings = ['scroll_delay', 'first_card_delay', 'wait_timeout', 'crop_area', 'crop_area_relative']
        
        for setting in expected_settings:
            if setting not in app.settings:
                print(f"✗ Missing setting: {setting}")
                return False
        
        # Check that capture_mode is not in settings
        if 'capture_mode' in app.settings:
            print("✗ capture_mode should be removed from settings")
            return False
        
        print("✓ Settings are properly configured")
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ Settings test failed: {e}")
        return False

def test_ui_elements():
    """Test that UI elements are properly updated"""
    try:
        from deepseek_v1 import ScreenshotCaptureApp
        import tkinter as tk
        
        root = tk.Tk()
        app = ScreenshotCaptureApp(root)
        
        # Check that capture mode combo is removed
        if hasattr(app, 'capture_mode_var'):
            print("✗ capture_mode_var should be removed")
            return False
        
        # Check that crop area elements exist
        if not hasattr(app, 'area_info_var'):
            print("✗ area_info_var should exist")
            return False
        
        if not hasattr(app, 'select_area_btn'):
            print("✗ select_area_btn should exist")
            return False
        
        if not hasattr(app, 'clear_area_btn'):
            print("✗ clear_area_btn should exist")
            return False
        
        print("✓ UI elements are properly configured")
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ UI elements test failed: {e}")
        return False

def test_methods_removed():
    """Test that smart preview methods are removed"""
    try:
        from deepseek_v1 import ScreenshotCaptureApp
        import tkinter as tk
        
        root = tk.Tk()
        app = ScreenshotCaptureApp(root)
        
        # Check that smart preview methods are removed
        removed_methods = [
            'get_preview_container_info',
            'capture_preview_element_direct',
            'capture_mobile_preview_smart',
            'capture_preview_content_smart',
            'capture_scrollable_preview_content',
            'debug_screenshot_test',
            '_has_meaningful_content'
        ]
        
        for method in removed_methods:
            if hasattr(app, method):
                print(f"✗ Method {method} should be removed")
                return False
        
        print("✓ Smart preview methods are properly removed")
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ Methods removal test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing Simplified Screenshot Capture")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_import),
        ("Settings Test", test_settings),
        ("UI Elements Test", test_ui_elements),
        ("Methods Removal Test", test_methods_removed)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"  Failed!")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The simplified capture is working correctly.")
        print("\nKey Features Verified:")
        print("- Only full page screenshot with crop area")
        print("- No smart preview or element targeting")
        print("- User-defined crop area support")
        print("- Consistent across different display dimensions")
    else:
        print("✗ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
